#!/usr/bin/env python3
"""
JARVIS Local Testing Script
Starts services and tests all features locally
"""

import subprocess
import time
import requests
import json
import sys
import os
from pathlib import Path

# Configuration
BACKEND_PORT = 8000
FRONTEND_PORT = 3000
BASE_URL = f"http://localhost:{BACKEND_PORT}"
FRONTEND_URL = f"http://localhost:{FRONTEND_PORT}"

class JARVISLocalTester:
    def __init__(self):
        self.services_started = False
        
    def print_header(self, text):
        print(f"\n{'='*60}")
        print(f"🤖 {text}")
        print(f"{'='*60}")
    
    def print_step(self, step, text):
        print(f"\n{step}. {text}")
        print("-" * 40)
    
    def check_prerequisites(self):
        """Check if required tools are available"""
        self.print_step("1", "Checking Prerequisites")
        
        # Check Python
        try:
            python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
            print(f"✅ {python_version}")
        except Exception as e:
            print(f"❌ Python check failed: {e}")
            return False
        
        # Check Node.js
        try:
            node_version = subprocess.check_output(["node", "--version"], text=True).strip()
            print(f"✅ Node.js {node_version}")
        except Exception as e:
            print(f"⚠️  Node.js not found: {e}")
            print("   Frontend testing will be limited")
        
        # Check if .env exists
        if Path(".env").exists():
            print("✅ .env file found")
        else:
            print("❌ .env file not found")
            print("   Please copy .env.example to .env and configure API keys")
            return False
        
        return True
    
    def install_dependencies(self):
        """Install required Python dependencies"""
        self.print_step("2", "Installing Dependencies")
        
        try:
            # Install core dependencies
            print("📦 Installing core Python dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "fastapi", "uvicorn[standard]", "python-dotenv", 
                "requests", "openai", "pydantic", "pydantic-settings"
            ], check=True, capture_output=True)
            print("✅ Core dependencies installed")
            
            # Try to install additional dependencies
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install",
                    "redis", "httpx", "aiofiles", "jinja2"
                ], check=True, capture_output=True)
                print("✅ Additional dependencies installed")
            except:
                print("⚠️  Some additional dependencies failed (non-critical)")
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Dependency installation failed: {e}")
            return False
    
    def start_backend(self):
        """Start the FastAPI backend"""
        self.print_step("3", "Starting Backend Service")
        
        try:
            # Change to backend directory
            os.chdir("backend")
            
            # Start uvicorn server
            print("🚀 Starting FastAPI backend...")
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "main:app", 
                "--host", "0.0.0.0", "--port", str(BACKEND_PORT), "--reload"
            ])
            
            # Wait for backend to start
            print("⏳ Waiting for backend to start...")
            for i in range(30):
                try:
                    response = requests.get(f"{BASE_URL}/health", timeout=2)
                    if response.status_code == 200:
                        print("✅ Backend started successfully!")
                        print(f"   URL: {BASE_URL}")
                        print(f"   API Docs: {BASE_URL}/docs")
                        return True
                except:
                    pass
                time.sleep(2)
                print(f"   Attempt {i+1}/30...")
            
            print("❌ Backend failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting backend: {e}")
            return False
        finally:
            # Return to root directory
            os.chdir("..")
    
    def test_backend_features(self):
        """Test all backend features"""
        self.print_step("4", "Testing Backend Features")
        
        tests = [
            ("Health Check", self.test_health),
            ("AI Chat", self.test_ai_chat),
            ("Voice TTS", self.test_voice_tts),
            ("System Info", self.test_system_info),
            ("API Documentation", self.test_api_docs)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 Testing {test_name}...")
                if test_func():
                    print(f"✅ {test_name}: PASS")
                    passed += 1
                else:
                    print(f"❌ {test_name}: FAIL")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
        
        print(f"\n📊 Backend Test Results: {passed}/{total} passed")
        return passed == total
    
    def test_health(self):
        """Test health endpoint"""
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    
    def test_ai_chat(self):
        """Test AI chat functionality"""
        payload = {
            "message": "Hello JARVIS, this is a test message",
            "user_id": "test_user"
        }
        response = requests.post(f"{BASE_URL}/api/v1/ai/chat", json=payload, timeout=30)
        if response.status_code == 200:
            data = response.json()
            return "content" in data and len(data["content"]) > 0
        return False
    
    def test_voice_tts(self):
        """Test text-to-speech"""
        payload = {
            "text": "This is a test of the voice synthesis system",
            "user_id": "test_user"
        }
        response = requests.post(f"{BASE_URL}/api/v1/voice/tts", json=payload, timeout=15)
        return response.status_code == 200
    
    def test_system_info(self):
        """Test system information"""
        response = requests.get(f"{BASE_URL}/api/v1/system/info", timeout=10)
        return response.status_code == 200
    
    def test_api_docs(self):
        """Test API documentation"""
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        return response.status_code == 200
    
    def start_frontend(self):
        """Start the React frontend"""
        self.print_step("5", "Starting Frontend (Optional)")
        
        try:
            if not Path("frontend/package.json").exists():
                print("⚠️  Frontend not found, skipping...")
                return True
            
            # Change to frontend directory
            os.chdir("frontend")
            
            # Install dependencies
            print("📦 Installing frontend dependencies...")
            subprocess.run(["npm", "install"], check=True, capture_output=True)
            
            # Start development server
            print("🚀 Starting React frontend...")
            self.frontend_process = subprocess.Popen(["npm", "run", "dev"])
            
            print(f"✅ Frontend starting at: {FRONTEND_URL}")
            print("   (May take a few moments to fully load)")
            
            return True
            
        except Exception as e:
            print(f"⚠️  Frontend start failed: {e}")
            print("   Backend testing will continue...")
            return True
        finally:
            # Return to root directory
            os.chdir("..")
    
    def show_access_info(self):
        """Show how to access JARVIS"""
        self.print_step("6", "Access Information")
        
        print("🌐 JARVIS is now running locally!")
        print(f"   Backend API: {BASE_URL}")
        print(f"   API Documentation: {BASE_URL}/docs")
        print(f"   Interactive API: {BASE_URL}/redoc")
        
        if hasattr(self, 'frontend_process'):
            print(f"   Frontend UI: {FRONTEND_URL}")
        
        print("\n🧪 Test Commands:")
        print(f"   curl {BASE_URL}/health")
        print(f"   curl -X POST {BASE_URL}/api/v1/ai/chat -H 'Content-Type: application/json' -d '{{\"message\":\"Hello\",\"user_id\":\"test\"}}'")
        
        print("\n⏹️  To stop services:")
        print("   Press Ctrl+C in this terminal")
    
    def cleanup(self):
        """Clean up processes"""
        print("\n🛑 Stopping services...")
        
        if hasattr(self, 'backend_process'):
            self.backend_process.terminate()
            print("✅ Backend stopped")
        
        if hasattr(self, 'frontend_process'):
            self.frontend_process.terminate()
            print("✅ Frontend stopped")
    
    def run_full_test(self):
        """Run complete local testing"""
        self.print_header("JARVIS Local Testing & Deployment")
        
        try:
            # Run all steps
            if not self.check_prerequisites():
                return False
            
            if not self.install_dependencies():
                return False
            
            if not self.start_backend():
                return False
            
            if not self.test_backend_features():
                print("⚠️  Some backend tests failed, but services are running")
            
            self.start_frontend()
            self.show_access_info()
            
            # Keep services running
            print("\n⏳ Services are running. Press Ctrl+C to stop...")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """Main function"""
    tester = JARVISLocalTester()
    success = tester.run_full_test()
    
    if success:
        print("\n🎉 JARVIS local testing completed successfully!")
        print("   Ready for GitHub deployment!")
    else:
        print("\n⚠️  Some issues encountered during testing")
        print("   Check the logs above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
