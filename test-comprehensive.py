#!/usr/bin/env python3
"""
Comprehensive test script for JARVIS AI Assistant
Tests all core functionality, optimized tech stack, and deployment readiness
"""

import requests
import json
import time
import sys
import os
import subprocess
from typing import Dict, Any, List
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
FRONTEND_URL = "http://localhost:3000"

class JARVISTestSuite:
    def __init__(self):
        self.test_results = []
        self.passed = 0
        self.failed = 0
        self.warnings = 0
        
    def log_test(self, test_name: str, success: bool, message: str = "", warning: bool = False):
        """Log test result"""
        if warning:
            status = "⚠️  WARN"
            self.warnings += 1
        else:
            status = "✅ PASS" if success else "❌ FAIL"
            if success:
                self.passed += 1
            else:
                self.failed += 1
        
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "warning": warning
        })
    
    def test_environment_setup(self):
        """Test environment configuration"""
        try:
            # Check .env file exists
            env_file = Path(".env")
            if not env_file.exists():
                self.log_test("Environment File", False, ".env file not found")
                return False
            
            # Check for required environment variables
            required_vars = [
                "OPENAI_API_KEY", "GOOGLE_API_KEY", "SUPABASE_URL", 
                "SUPABASE_KEY", "LIVEKIT_URL"
            ]
            
            missing_vars = []
            with open(".env", "r") as f:
                env_content = f.read()
                for var in required_vars:
                    if f"{var}=" not in env_content or f"{var}=your_" in env_content:
                        missing_vars.append(var)
            
            if missing_vars:
                self.log_test("Environment Variables", False, 
                            f"Missing or placeholder values: {', '.join(missing_vars)}")
                return False
            else:
                self.log_test("Environment Variables", True, "All required variables configured")
                return True
                
        except Exception as e:
            self.log_test("Environment Setup", False, f"Error: {str(e)}")
            return False
    
    def test_health_check(self):
        """Test basic health endpoint"""
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            success = response.status_code == 200
            
            if success:
                data = response.json()
                status = data.get('status', 'unknown')
                uptime = data.get('uptime', 'unknown')
                message = f"Status: {status}, Uptime: {uptime}"
            else:
                message = f"HTTP {response.status_code}"
                
        except Exception as e:
            success = False
            message = f"Connection error: {str(e)}"
        
        self.log_test("Backend Health", success, message)
        return success
    
    def test_ai_services(self):
        """Test AI functionality with optimized models"""
        try:
            payload = {
                "message": "Hello JARVIS, test your AI capabilities",
                "user_id": "test_user_ai"
            }
            
            response = requests.post(
                f"{API_BASE}/ai/chat",
                json=payload,
                timeout=30
            )
            
            success = response.status_code == 200
            
            if success:
                data = response.json()
                if "content" in data and len(data["content"]) > 10:
                    model_used = data.get("model_used", "unknown")
                    confidence = data.get("confidence", 0)
                    message = f"AI response received, Model: {model_used}, Confidence: {confidence:.2f}"
                else:
                    success = False
                    message = "Invalid AI response format"
            else:
                message = f"HTTP {response.status_code}"
                
        except Exception as e:
            success = False
            message = f"Error: {str(e)}"
        
        self.log_test("AI Services", success, message)
        return success
    
    def test_whisper_voice_processing(self):
        """Test Whisper-based voice processing"""
        try:
            # Test TTS endpoint
            payload = {
                "text": "Testing Whisper-based voice processing system",
                "user_id": "test_user_voice",
                "language": "en"
            }
            
            response = requests.post(
                f"{API_BASE}/voice/tts",
                json=payload,
                timeout=15
            )
            
            success = response.status_code == 200
            
            if success:
                content_type = response.headers.get('content-type', '')
                if 'audio' in content_type:
                    message = f"Audio generated successfully, Type: {content_type}"
                elif 'json' in content_type:
                    data = response.json()
                    if "audio_url" in data or "audio_data" in data:
                        message = "Voice synthesis successful (JSON response)"
                    else:
                        message = "Voice response received"
                else:
                    message = f"Unexpected content type: {content_type}"
            else:
                message = f"HTTP {response.status_code}"
                
        except Exception as e:
            success = False
            message = f"Error: {str(e)}"
        
        self.log_test("Whisper Voice Processing", success, message)
        return success
    
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 JARVIS Comprehensive Test Suite")
        print("Testing optimized tech stack and deployment readiness")
        print("=" * 60)
        
        # Environment and setup tests
        print("\n📋 Environment & Setup Tests")
        self.test_environment_setup()
        
        # Core service tests
        print("\n🔧 Core Service Tests")
        self.test_health_check()
        
        # AI and ML tests
        print("\n🧠 AI & ML Tests")
        self.test_ai_services()
        self.test_whisper_voice_processing()
        
        # Print comprehensive summary
        print("\n" + "=" * 60)
        print(f"📊 Comprehensive Test Results")
        print(f"✅ Passed: {self.passed}")
        print(f"❌ Failed: {self.failed}")
        print(f"⚠️  Warnings: {self.warnings}")
        
        total_tests = self.passed + self.failed
        if total_tests > 0:
            success_rate = (self.passed / total_tests) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.failed == 0:
            print("\n🎉 All critical tests passed! JARVIS is ready for deployment!")
            if self.warnings > 0:
                print(f"⚠️  Note: {self.warnings} warning(s) - check logs for optimization opportunities")
            return True
        else:
            print(f"\n⚠️  {self.failed} critical test(s) failed. Address these issues before deployment.")
            return False

def check_prerequisites():
    """Check if prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check if Docker is running
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        print("✅ Docker is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker is not available or not running")
        return False
    
    return True

def main():
    """Main test function"""
    print("🤖 JARVIS AI Assistant - Comprehensive Test Suite")
    print("Testing optimized tech stack with Whisper, multi-AI models, and deployment readiness")
    print(f"Backend URL: {BASE_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please install Docker.")
        sys.exit(1)
    
    # Wait for services to be ready
    print("⏳ Waiting for services to be ready...")
    max_retries = 30
    
    for i in range(max_retries):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=3)
            if response.status_code == 200:
                print("✅ Backend is ready!")
                break
        except:
            pass
        
        if i == max_retries - 1:
            print("❌ Backend is not responding.")
            print("Please run: docker-compose up -d")
            sys.exit(1)
        
        time.sleep(2)
        if i % 10 == 0:
            print(f"   Waiting... ({i+1}/{max_retries})")
    
    # Run comprehensive tests
    test_suite = JARVISTestSuite()
    success = test_suite.run_all_tests()
    
    # Provide deployment guidance
    if success:
        print("\n🚀 Deployment Recommendations:")
        print("1. ✅ Local testing complete - ready for cloud deployment")
        print("2. 🌐 Frontend: Deploy to Vercel/Netlify/GitHub Pages")
        print("3. ⚡ Backend: Deploy to Railway/Render/Fly.io")
        print("4. 🗄️  Database: Already using Supabase (cloud-ready)")
    else:
        print("\n🔧 Fix the failed tests before deployment:")
        print("1. Check .env configuration")
        print("2. Verify all services are running")
        print("3. Check API key validity")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
