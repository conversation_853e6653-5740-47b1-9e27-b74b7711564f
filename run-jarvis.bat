@echo off
echo ========================================
echo         JARVIS AI Assistant
echo    Complete Startup Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://python.org
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo Python version: 
python --version
echo Node.js version: 
node --version
echo.

echo ========================================
echo    Starting JARVIS Components
echo ========================================
echo.

echo [1/2] Starting Backend Server...
start "JARVIS Backend" cmd /k "cd backend && python -m venv venv && call venv\Scripts\activate.bat && pip install -r requirements.txt && set DEBUG=True && set DATABASE_URL=sqlite:///./jarvis.db && set SECRET_KEY=dev-secret-key && python main.py"

echo Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

echo.
echo [2/2] Starting Frontend Application...
start "JARVIS Frontend" cmd /k "cd frontend && npm install && npm run dev"

echo.
echo ========================================
echo       JARVIS is Starting Up!
echo ========================================
echo.
echo Backend API: http://localhost:8000
echo Frontend UI: http://localhost:3000
echo API Docs: http://localhost:8000/docs
echo.
echo Both services are starting in separate windows.
echo Wait a moment for them to fully initialize.
echo.
echo To stop JARVIS, close both command windows.
echo.

pause
