#!/usr/bin/env python3
"""
Basic JARVIS functionality test script
Tests core components without heavy dependencies
"""

import sys
import os
import json
import time
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

def test_imports():
    """Test if basic imports work"""
    print("🔍 Testing basic imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        import pydantic
        print("✅ Pydantic imported successfully")
    except ImportError as e:
        print(f"❌ Pydantic import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n🔧 Testing configuration...")
    
    try:
        from app.core.config import settings
        print("✅ Configuration loaded successfully")
        print(f"   Debug mode: {settings.DEBUG}")
        print(f"   Database URL: {settings.DATABASE_URL[:50]}...")
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_basic_api():
    """Test basic API structure"""
    print("\n🚀 Testing basic API structure...")
    
    try:
        from fastapi import FastAPI
        from app.core.config import settings
        
        app = FastAPI(
            title="JARVIS Test",
            description="Basic test of JARVIS API",
            version="1.0.0"
        )
        
        @app.get("/")
        async def root():
            return {"message": "JARVIS Test API", "status": "working"}
        
        @app.get("/health")
        async def health():
            return {"status": "healthy", "timestamp": time.time()}
        
        print("✅ Basic API structure created successfully")
        return True
    except Exception as e:
        print(f"❌ API structure creation failed: {e}")
        return False

def test_environment():
    """Test environment variables"""
    print("\n🌍 Testing environment variables...")
    
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
        
        # Read some basic env vars
        from dotenv import load_dotenv
        load_dotenv()
        
        debug = os.getenv("DEBUG", "false")
        secret_key = os.getenv("SECRET_KEY", "")
        
        print(f"   DEBUG: {debug}")
        print(f"   SECRET_KEY: {'*' * min(len(secret_key), 10) if secret_key else 'Not set'}")
        
        return True
    else:
        print("❌ .env file not found")
        return False

def test_database_config():
    """Test database configuration"""
    print("\n🗄️ Testing database configuration...")
    
    try:
        from app.core.config import settings
        db_url = settings.DATABASE_URL
        
        if "postgresql" in db_url:
            print("✅ PostgreSQL database configured")
        elif "sqlite" in db_url:
            print("✅ SQLite database configured")
        else:
            print(f"⚠️ Unknown database type: {db_url}")
        
        return True
    except Exception as e:
        print(f"❌ Database configuration test failed: {e}")
        return False

def test_file_structure():
    """Test file structure"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "backend/main.py",
        "backend/app/__init__.py",
        "backend/app/core/config.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "docker-compose.dev.yml",
        ".env"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True

def generate_test_report(results):
    """Generate a test report"""
    print("\n" + "="*50)
    print("📊 JARVIS BASIC TEST REPORT")
    print("="*50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! JARVIS basic setup is working.")
        return True
    else:
        print(f"\n⚠️ {total_tests - passed_tests} test(s) failed. Please check the issues above.")
        return False

def main():
    """Run all tests"""
    print("🚀 JARVIS Basic Functionality Test")
    print("=" * 40)
    
    # Change to project directory
    os.chdir(Path(__file__).parent)
    
    # Run tests
    results = {
        "File Structure": test_file_structure(),
        "Environment Variables": test_environment(),
        "Basic Imports": test_imports(),
        "Configuration": test_config(),
        "Database Config": test_database_config(),
        "API Structure": test_basic_api(),
    }
    
    # Generate report
    success = generate_test_report(results)
    
    if success:
        print("\n🎯 Next Steps:")
        print("1. Install dependencies: pip install -r backend/requirements.txt")
        print("2. Start database: docker-compose -f docker-compose.dev.yml up -d postgres redis")
        print("3. Run backend: cd backend && uvicorn main:app --reload")
        print("4. Run frontend: cd frontend && npm install && npm run dev")
        print("5. Access JARVIS at http://localhost:3000")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
