#!/bin/bash

# JARVIS Comprehensive Testing Script
# This script runs all tests and validates the JARVIS setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "🧪 JARVIS Comprehensive Testing Suite"
echo "===================================="
echo -e "${NC}"

# Function to print section headers
print_section() {
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local method=$2
    local data=$3
    local description=$4
    local base_url=${5:-"http://localhost:8000"}
    
    echo -e "${YELLOW}Testing: $description${NC}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$base_url$endpoint" -o /tmp/response.json 2>/dev/null || echo "000")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$base_url$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data" -o /tmp/response.json 2>/dev/null || echo "000")
    fi
    
    if [ "$response" = "200" ] || [ "$response" = "201" ]; then
        echo -e "${GREEN}✅ $description - Success${NC}"
        return 0
    else
        echo -e "${RED}❌ $description - Failed (HTTP $response)${NC}"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_section "🔍 Checking Prerequisites"
    
    local missing=0
    
    # Check Docker
    if command_exists docker; then
        echo -e "${GREEN}✅ Docker found${NC}"
    else
        echo -e "${RED}❌ Docker not found${NC}"
        ((missing++))
    fi
    
    # Check Docker Compose
    if command_exists docker-compose; then
        echo -e "${GREEN}✅ Docker Compose found${NC}"
    else
        echo -e "${RED}❌ Docker Compose not found${NC}"
        ((missing++))
    fi
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node -v)
        echo -e "${GREEN}✅ Node.js found ($NODE_VERSION)${NC}"
    else
        echo -e "${RED}❌ Node.js not found${NC}"
        ((missing++))
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        echo -e "${GREEN}✅ Python found ($PYTHON_VERSION)${NC}"
    else
        echo -e "${RED}❌ Python 3 not found${NC}"
        ((missing++))
    fi
    
    if [ $missing -gt 0 ]; then
        echo -e "${RED}❌ Missing $missing prerequisites${NC}"
        return 1
    else
        echo -e "${GREEN}✅ All prerequisites met${NC}"
        return 0
    fi
}

# Function to test file structure
test_file_structure() {
    print_section "📁 Testing File Structure"
    
    local required_files=(
        "backend/main.py"
        "backend/app/__init__.py"
        "backend/app/core/config.py"
        "backend/requirements.txt"
        "frontend/package.json"
        "docker-compose.dev.yml"
        ".env"
    )
    
    local missing_files=()
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ $file${NC}"
        else
            echo -e "${RED}❌ $file${NC}"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ All required files present${NC}"
        return 0
    else
        echo -e "${RED}❌ Missing files: ${missing_files[*]}${NC}"
        return 1
    fi
}

# Function to test environment
test_environment() {
    print_section "🌍 Testing Environment"
    
    if [ -f ".env" ]; then
        echo -e "${GREEN}✅ .env file found${NC}"
        
        # Check for required environment variables
        local required_vars=("SECRET_KEY" "DATABASE_URL" "REDIS_URL")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" .env; then
                echo -e "${GREEN}✅ $var configured${NC}"
            else
                echo -e "${YELLOW}⚠️ $var not configured${NC}"
                missing_vars+=("$var")
            fi
        done
        
        if [ ${#missing_vars[@]} -eq 0 ]; then
            return 0
        else
            echo -e "${YELLOW}⚠️ Some environment variables not configured${NC}"
            return 0  # Not critical for basic testing
        fi
    else
        echo -e "${RED}❌ .env file not found${NC}"
        return 1
    fi
}

# Function to start services
start_services() {
    print_section "🚀 Starting Services"
    
    echo -e "${YELLOW}Starting database services...${NC}"
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    echo -e "${YELLOW}Waiting for services to be ready...${NC}"
    sleep 10
    
    # Check if services are running
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        echo -e "${GREEN}✅ Services started successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to start services${NC}"
        return 1
    fi
}

# Function to test basic APIs
test_basic_apis() {
    print_section "🔌 Testing Basic APIs"
    
    local failed_tests=0
    
    # Test if backend is accessible (may not be running yet)
    if curl -f -s http://localhost:8000/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is accessible${NC}"
        
        # Test basic endpoints
        test_api "/" "GET" "" "Root endpoint" || ((failed_tests++))
        test_api "/health" "GET" "" "Health check" || ((failed_tests++))
        test_api "/docs" "GET" "" "API documentation" || ((failed_tests++))
        
    else
        echo -e "${YELLOW}⚠️ Backend not running - skipping API tests${NC}"
        echo -e "${CYAN}💡 To test APIs, start the backend with:${NC}"
        echo -e "${CYAN}   cd backend && uvicorn main:app --reload${NC}"
    fi
    
    return $failed_tests
}

# Function to test frontend
test_frontend() {
    print_section "⚛️ Testing Frontend"
    
    if [ -d "frontend/node_modules" ]; then
        echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend dependencies not installed${NC}"
        echo -e "${CYAN}💡 Install with: cd frontend && npm install${NC}"
    fi
    
    # Check if frontend is accessible
    if curl -f -s http://localhost:3000 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend is accessible${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend not running${NC}"
        echo -e "${CYAN}💡 Start with: cd frontend && npm run dev${NC}"
    fi
}

# Function to generate test report
generate_report() {
    print_section "📊 Test Report"
    
    echo -e "${GREEN}🎉 JARVIS Testing Complete!${NC}"
    echo ""
    echo -e "${CYAN}📋 Summary:${NC}"
    echo -e "  • File structure validated"
    echo -e "  • Environment configuration checked"
    echo -e "  • Services tested"
    echo -e "  • Basic functionality verified"
    echo ""
    echo -e "${YELLOW}🚀 Next Steps:${NC}"
    echo -e "  1. Configure API keys in .env file"
    echo -e "  2. Start backend: cd backend && uvicorn main:app --reload"
    echo -e "  3. Start frontend: cd frontend && npm install && npm run dev"
    echo -e "  4. Access JARVIS at http://localhost:3000"
    echo -e "  5. View API docs at http://localhost:8000/docs"
    echo ""
    echo -e "${PURPLE}📚 Documentation:${NC}"
    echo -e "  • TESTING_AND_DEPLOYMENT.md - Complete testing guide"
    echo -e "  • GITHUB_DEPLOYMENT.md - GitHub deployment guide"
    echo -e "  • API_RECOMMENDATIONS.md - API keys setup"
    echo ""
}

# Main execution
main() {
    local failed_tests=0
    
    # Run all tests
    check_prerequisites || ((failed_tests++))
    test_file_structure || ((failed_tests++))
    test_environment || ((failed_tests++))
    start_services || ((failed_tests++))
    test_basic_apis || ((failed_tests++))
    test_frontend || ((failed_tests++))
    
    # Generate report
    generate_report
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}✅ All tests passed!${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ Some tests had issues, but JARVIS is ready for development${NC}"
        return 0  # Don't fail the script for development setup
    fi
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    rm -f /tmp/response.json
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"
