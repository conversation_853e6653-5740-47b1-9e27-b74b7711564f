# 🚀 JARVIS Deployment Guide

## 📋 Overview

This guide covers all deployment options for JARVIS - from local development to production cloud deployment.

## 🏠 Local Development

### Prerequisites
- **Python 3.11+** - [Download](https://python.org)
- **Node.js 18+** - [Download](https://nodejs.org)
- **Git** - [Download](https://git-scm.com)

### Quick Start (Windows)

1. **Clone Repository**
```bash
git clone https://github.com/yourusername/jarvis.git
cd jarvis
```

2. **Setup Environment**
```bash
# Copy environment template
copy .env.example .env

# Edit .env file with your API keys
notepad .env
```

3. **Run JARVIS**
```bash
# Start both frontend and backend
run-jarvis.bat
```

4. **Access Applications**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs
- Login: admin / admin123

### Manual Setup

#### Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run backend
python main.py
```

#### Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

## 🐳 Docker Deployment

### Local Docker Setup
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Docker
```bash
# Build for production
docker-compose -f docker-compose.yml up -d

# Scale services
docker-compose up -d --scale backend=3
```

## ☁️ Cloud Deployment

### 1. Vercel (Frontend) + Railway (Backend)

#### Frontend to Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy frontend
cd frontend
vercel --prod
```

#### Backend to Railway
1. Connect GitHub repo to Railway
2. Set environment variables
3. Deploy automatically

### 2. Netlify (Frontend) + Heroku (Backend)

#### Frontend to Netlify
```bash
# Build frontend
cd frontend
npm run build

# Deploy to Netlify (drag & drop dist folder)
```

#### Backend to Heroku
```bash
# Install Heroku CLI
# Create Heroku app
heroku create jarvis-backend

# Set environment variables
heroku config:set OPENAI_API_KEY=your_key

# Deploy
git push heroku main
```

### 3. AWS Deployment

#### Frontend to S3 + CloudFront
```bash
# Build frontend
npm run build

# Upload to S3
aws s3 sync dist/ s3://your-bucket-name

# Configure CloudFront distribution
```

#### Backend to EC2/ECS
```bash
# Create EC2 instance
# Install Docker
# Run container
docker run -d -p 8000:8000 your-backend-image
```

## 🔧 Environment Configuration

### Required Environment Variables

```env
# Core AI Services
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_google_key

# Database
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Security
SECRET_KEY=your_secret_key
```

### Optional Services
```env
# Weather
WEATHER_API_KEY=your_weather_key

# News
NEWS_API_KEY=your_news_key

# Communication
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
```

## 🔒 Security Configuration

### Production Security Checklist
- [ ] Change default SECRET_KEY
- [ ] Use HTTPS in production
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable authentication
- [ ] Use environment variables for secrets
- [ ] Configure firewall rules
- [ ] Set up monitoring

### HTTPS Setup
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
    }
}
```

## 📊 Monitoring & Logging

### Health Checks
```bash
# Backend health
curl http://localhost:8000/health

# Frontend health
curl http://localhost:3000
```

### Logging Configuration
```python
# backend/app/core/logging.py
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy JARVIS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.11'
          
      - name: Deploy Frontend
        run: |
          cd frontend
          npm install
          npm run build
          # Deploy to your hosting service
          
      - name: Deploy Backend
        run: |
          cd backend
          pip install -r requirements.txt
          # Deploy to your hosting service
```

## 🚨 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check Python version
python --version

# Check dependencies
pip list

# Check logs
python main.py
```

#### Frontend Build Fails
```bash
# Clear cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check Node version
node --version
```

#### Database Connection Issues
```bash
# Check environment variables
echo $DATABASE_URL

# Test connection
python -c "import psycopg2; print('OK')"
```

### Performance Optimization

#### Backend Optimization
- Use Redis for caching
- Enable gzip compression
- Optimize database queries
- Use connection pooling

#### Frontend Optimization
- Enable code splitting
- Optimize images
- Use CDN for static assets
- Enable service worker

## 📞 Support

### Getting Help
- **Documentation**: Check this guide first
- **GitHub Issues**: Report bugs and feature requests
- **Community**: Join our Discord server
- **Email**: <EMAIL>

### Deployment Services Comparison

| Service | Frontend | Backend | Database | Cost |
|---------|----------|---------|----------|------|
| Vercel + Railway | ✅ | ✅ | ❌ | $$ |
| Netlify + Heroku | ✅ | ✅ | ❌ | $$ |
| AWS | ✅ | ✅ | ✅ | $$$ |
| Google Cloud | ✅ | ✅ | ✅ | $$$ |
| Azure | ✅ | ✅ | ✅ | $$$ |

### Recommended Stack
- **Development**: Local setup with run-jarvis.bat
- **Staging**: Docker Compose
- **Production**: Vercel + Railway + Supabase

---

**🎉 Congratulations!** Your JARVIS AI Assistant is now deployed and ready to help with voice commands, automation, healthcare monitoring, and much more!
