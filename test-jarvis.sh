#!/bin/bash

# JARVIS Testing and Deployment Script
# This script tests all components and deploys JARVIS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="http://localhost:8000"
FRONTEND_URL="http://localhost:3000"
TEST_USER_ID="test_user_$(date +%s)"

echo -e "${BLUE}"
echo "🚀 JARVIS Testing and Deployment Script"
echo "======================================"
echo -e "${NC}"

# Function to print section headers
print_section() {
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if service is running
check_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Checking $name...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $name is running${NC}"
            return 0
        fi
        echo -e "${CYAN}Waiting for $name... ($attempt/$max_attempts)${NC}"
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ $name is not responding${NC}"
    return 1
}

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local method=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}Testing: $description${NC}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$BACKEND_URL$endpoint" -o /tmp/response.json)
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$BACKEND_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data" -o /tmp/response.json)
    fi
    
    if [ "$response" = "200" ] || [ "$response" = "201" ]; then
        echo -e "${GREEN}✅ $description - Success${NC}"
        return 0
    else
        echo -e "${RED}❌ $description - Failed (HTTP $response)${NC}"
        echo -e "${YELLOW}Response:${NC}"
        cat /tmp/response.json 2>/dev/null || echo "No response body"
        return 1
    fi
}

# Function to run comprehensive tests
run_tests() {
    print_section "🧪 Running Comprehensive Tests"
    
    local failed_tests=0
    
    # Test basic endpoints
    test_api "/" "GET" "" "Root endpoint" || ((failed_tests++))
    test_api "/health" "GET" "" "Health check" || ((failed_tests++))
    
    # Test AI service
    test_api "/api/v1/ai/chat" "POST" '{"message": "Hello JARVIS", "user_id": "'$TEST_USER_ID'"}' "AI Chat" || ((failed_tests++))
    
    # Test voice service
    test_api "/api/v1/voice/tts" "POST" '{"text": "Hello World", "user_id": "'$TEST_USER_ID'"}' "Text-to-Speech" || ((failed_tests++))
    
    # Test system info
    test_api "/api/v1/system/info" "GET" "" "System Information" || ((failed_tests++))
    
    # Test analytics
    test_api "/api/v1/analytics/usage" "GET" "" "Usage Analytics" || ((failed_tests++))
    
    echo ""
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        return 0
    else
        echo -e "${RED}❌ $failed_tests tests failed${NC}"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_section "🔍 Checking Prerequisites"
    
    local missing=0
    
    # Check Docker
    if command -v docker >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker found${NC}"
    else
        echo -e "${RED}❌ Docker not found${NC}"
        ((missing++))
    fi
    
    # Check Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker Compose found${NC}"
    else
        echo -e "${RED}❌ Docker Compose not found${NC}"
        ((missing++))
    fi
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node -v)
        echo -e "${GREEN}✅ Node.js found ($NODE_VERSION)${NC}"
    else
        echo -e "${RED}❌ Node.js not found${NC}"
        ((missing++))
    fi
    
    # Check Python
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 --version)
        echo -e "${GREEN}✅ Python found ($PYTHON_VERSION)${NC}"
    else
        echo -e "${RED}❌ Python 3 not found${NC}"
        ((missing++))
    fi
    
    if [ $missing -gt 0 ]; then
        echo -e "${RED}❌ Missing $missing prerequisites${NC}"
        return 1
    else
        echo -e "${GREEN}✅ All prerequisites met${NC}"
        return 0
    fi
}

# Function to start services
start_services() {
    print_section "🚀 Starting JARVIS Services"
    
    echo -e "${YELLOW}Starting with Docker Compose...${NC}"
    docker-compose up -d
    
    echo -e "${YELLOW}Waiting for services to start...${NC}"
    sleep 30
    
    # Check if services are running
    check_service "$BACKEND_URL/health" "Backend API"
    check_service "$FRONTEND_URL" "Frontend Web App"
}

# Function to stop services
stop_services() {
    print_section "🛑 Stopping Services"
    
    echo -e "${YELLOW}Stopping Docker Compose services...${NC}"
    docker-compose down
    
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Function to clean up
cleanup() {
    print_section "🧹 Cleaning Up"
    
    # Remove temporary files
    rm -f /tmp/response.json
    
    # Clean up Docker (optional)
    if [ "$1" = "full" ]; then
        echo -e "${YELLOW}Performing full cleanup...${NC}"
        docker-compose down -v
        docker system prune -f
    fi
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Function to display access information
display_access_info() {
    print_section "🌐 Access Information"
    
    echo -e "${GREEN}🎉 JARVIS is now running!${NC}"
    echo ""
    echo -e "${CYAN}Frontend (Web Interface):${NC}"
    echo -e "  🌐 $FRONTEND_URL"
    echo ""
    echo -e "${CYAN}Backend API:${NC}"
    echo -e "  🔗 $BACKEND_URL"
    echo -e "  📚 API Documentation: $BACKEND_URL/docs"
    echo -e "  🔍 Interactive API: $BACKEND_URL/redoc"
    echo ""
    echo -e "${CYAN}Monitoring & Management:${NC}"
    echo -e "  📊 Grafana: http://localhost:3001 (admin/admin123)"
    echo -e "  📈 Prometheus: http://localhost:9090"
    echo -e "  🌸 Flower: http://localhost:5555"
    echo -e "  🔧 N8N: http://localhost:5678 (admin/admin123)"
    echo ""
    echo -e "${YELLOW}📖 Next Steps:${NC}"
    echo -e "  1. Edit .env file with your API keys"
    echo -e "  2. See API_RECOMMENDATIONS.md for free API keys"
    echo -e "  3. See FEATURES_AND_TESTING.md for testing guide"
    echo -e "  4. See DEPLOYMENT.md for production deployment"
    echo ""
}

# Main execution
case "$1" in
    "test")
        check_prerequisites
        run_tests
        ;;
    "start")
        check_prerequisites
        start_services
        display_access_info
        ;;
    "stop")
        stop_services
        ;;
    "clean")
        cleanup
        ;;
    "clean-full")
        cleanup full
        ;;
    "deploy")
        check_prerequisites
        start_services
        run_tests
        display_access_info
        ;;
    *)
        echo -e "${YELLOW}Usage: $0 {test|start|stop|clean|clean-full|deploy}${NC}"
        echo ""
        echo -e "${CYAN}Commands:${NC}"
        echo -e "  test       - Run comprehensive tests"
        echo -e "  start      - Start all services"
        echo -e "  stop       - Stop all services"
        echo -e "  clean      - Clean up temporary files"
        echo -e "  clean-full - Full cleanup including Docker volumes"
        echo -e "  deploy     - Full deployment with testing"
        echo ""
        exit 1
        ;;
esac
