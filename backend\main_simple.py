#!/usr/bin/env python3
"""
Simplified JARVIS Backend for Testing
Core functionality without complex dependencies
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import time
import os
from typing import Dict, Any, Optional
import json

# Simple configuration
class Settings:
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")

settings = Settings()

# Create FastAPI app
app = FastAPI(
    title="JARVIS AI Assistant - Test Mode",
    description="Simplified JARVIS for local testing",
    version="1.0.0-test"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    user_id: str

class ChatResponse(BaseModel):
    content: str
    model_used: str = "test-mode"
    confidence: float = 0.95

class TTSRequest(BaseModel):
    text: str
    user_id: str
    language: str = "en"

class TTSResponse(BaseModel):
    status: str
    message: str
    audio_url: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    timestamp: float
    uptime: str
    version: str

class SystemInfoResponse(BaseModel):
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    status: str

# Global variables
start_time = time.time()

# Routes
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "JARVIS AI Assistant - Test Mode",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    uptime_seconds = time.time() - start_time
    uptime_str = f"{int(uptime_seconds // 3600)}h {int((uptime_seconds % 3600) // 60)}m {int(uptime_seconds % 60)}s"
    
    return HealthResponse(
        status="healthy",
        timestamp=time.time(),
        uptime=uptime_str,
        version="1.0.0-test"
    )

@app.post("/api/v1/ai/chat", response_model=ChatResponse)
async def ai_chat(request: ChatRequest):
    """AI chat endpoint - simplified for testing"""
    try:
        # Simulate AI processing
        time.sleep(0.5)  # Simulate processing time
        
        # Simple response based on input
        message = request.message.lower()
        
        if "hello" in message or "hi" in message:
            response = f"Hello! I'm JARVIS, your AI assistant. How can I help you today?"
        elif "test" in message:
            response = f"Test successful! JARVIS is working properly. Your message was: '{request.message}'"
        elif "features" in message:
            response = "I can help with AI chat, voice synthesis, computer vision, automation, and much more!"
        elif "voice" in message:
            response = "My voice processing uses OpenAI Whisper for speech recognition and Google TTS for synthesis - all optimized for performance!"
        elif "deploy" in message:
            response = "I'm ready for deployment! You can deploy me to Vercel (frontend) + Railway (backend) + Supabase (database)."
        else:
            response = f"I received your message: '{request.message}'. I'm running in test mode with optimized Whisper voice processing and multi-AI capabilities!"
        
        return ChatResponse(
            content=response,
            model_used="jarvis-test-mode",
            confidence=0.95
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI processing error: {str(e)}")

@app.post("/api/v1/voice/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """Text-to-speech endpoint - simplified for testing"""
    try:
        # Simulate TTS processing
        time.sleep(0.3)
        
        return TTSResponse(
            status="success",
            message=f"TTS processing completed for: '{request.text[:50]}...' in {request.language}",
            audio_url="/audio/generated/test.mp3"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS error: {str(e)}")

@app.get("/api/v1/system/info", response_model=SystemInfoResponse)
async def system_info():
    """System information endpoint"""
    try:
        import psutil
        
        return SystemInfoResponse(
            cpu_usage=psutil.cpu_percent(),
            memory_usage=psutil.virtual_memory().percent,
            disk_usage=psutil.disk_usage('/').percent,
            status="operational"
        )
    except ImportError:
        # Fallback if psutil not available
        return SystemInfoResponse(
            cpu_usage=25.0,
            memory_usage=45.0,
            disk_usage=60.0,
            status="operational (simulated)"
        )

@app.get("/api/v1/voice/test")
async def voice_test():
    """Voice processing test endpoint"""
    return {
        "whisper_available": True,
        "tts_available": True,
        "supported_languages": ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"],
        "whisper_model": "base",
        "processing_mode": "local",
        "status": "optimized"
    }

@app.get("/api/v1/ai/models")
async def ai_models():
    """Available AI models"""
    return {
        "available_models": [
            {"name": "OpenAI GPT", "status": "configured" if settings.OPENAI_API_KEY else "not_configured"},
            {"name": "Google Gemini", "status": "configured" if settings.GOOGLE_API_KEY else "not_configured"},
            {"name": "Whisper (Voice)", "status": "available"},
            {"name": "Local Processing", "status": "available"}
        ],
        "optimizations": [
            "Whisper for FREE voice processing",
            "Multi-AI model fallbacks",
            "Local processing for privacy",
            "Optimized for deployment"
        ]
    }

@app.get("/api/v1/features")
async def features():
    """List available features"""
    return {
        "core_features": [
            "AI Chat (Multi-model support)",
            "Voice Processing (Whisper + TTS)",
            "Computer Vision",
            "Smart Home Control",
            "Automation",
            "Document Generation",
            "Web Scraping",
            "Healthcare Monitoring",
            "Booking Services"
        ],
        "optimizations": [
            "FREE Whisper voice processing (vs paid Azure)",
            "Multi-AI redundancy (OpenAI, Gemini, Claude)",
            "Local processing for privacy",
            "Cloud-ready with Supabase",
            "Docker containerized",
            "Performance optimized"
        ],
        "deployment_ready": True,
        "test_mode": True
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {
        "error": "Endpoint not found",
        "message": "This endpoint is not available in test mode",
        "available_endpoints": [
            "/health",
            "/api/v1/ai/chat",
            "/api/v1/voice/tts",
            "/api/v1/system/info",
            "/api/v1/features",
            "/docs"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
