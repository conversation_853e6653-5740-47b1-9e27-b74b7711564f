global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "jarvis-backend"
    static_configs:
      - targets: ["backend:8000"]
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: "jarvis-frontend"
    static_configs:
      - targets: ["frontend:3000"]
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: "postgres"
    static_configs:
      - targets: ["postgres:5432"]
    scrape_interval: 30s

  - job_name: "redis"
    static_configs:
      - targets: ["redis:6379"]
    scrape_interval: 30s

  - job_name: "livekit"
    static_configs:
      - targets: ["livekit:7880"]
    scrape_interval: 30s

  - job_name: "n8n"
    static_configs:
      - targets: ["n8n:5678"]
    scrape_interval: 30s

  - job_name: "elasticsearch"
    static_configs:
      - targets: ["elasticsearch:9200"]
    scrape_interval: 30s
