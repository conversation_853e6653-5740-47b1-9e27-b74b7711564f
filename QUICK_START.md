# 🚀 JARVIS Quick Start Guide

## ⚡ 30-Second Setup

1. **Clone & Setup**
```bash
git clone https://github.com/yourusername/jarvis.git
cd jarvis
copy .env.example .env
```

2. **Add API Key** (Edit `.env` file)
```env
OPENAI_API_KEY=your_openai_api_key_here
```

3. **Run JARVIS**
```bash
run-jarvis.bat
```

4. **Access JARVIS**
- Frontend: http://localhost:3000
- Login: admin / admin123

## 🎯 What You Get

### ✅ Complete JARVIS Features
- **🗣️ Voice Assistant** - Natural conversation with wake words
- **👁️ Computer Vision** - Object detection, face recognition, OCR
- **🏥 Healthcare** - Symptom analysis, vital signs monitoring
- **📅 Booking** - Flights, hotels, restaurants, appointments
- **🏠 Smart Home** - Device control, automation, scenes
- **🤖 Automation** - AI-powered workflows and rules
- **⚙️ Device Control** - Mobile, laptop, IoT device management
- **🔧 Settings** - Personalization and preferences

### 🌟 Advanced Capabilities
- **Multi-AI Support** - OpenAI GPT-4, Google Gemini, Anthropic Claude
- **Voice Processing** - Whisper speech recognition + TTS
- **Real-time Communication** - WebRTC with LiveKit
- **Cross-platform** - Web, mobile, desktop ready
- **Zero Human Intervention** - Fully autonomous operations
- **Emergency Services** - Automatic emergency detection & response
- **Learning & Adaptation** - Behavioral pattern recognition

## 📁 Workspace Structure

```
JARVIS/
├── 📁 backend/           # Python FastAPI backend
│   ├── 📁 app/          # Application modules
│   ├── 📁 database/     # Database schema & migrations
│   ├── 📄 main.py       # Main application entry
│   └── 📄 requirements.txt # Python dependencies
├── 📁 frontend/         # React TypeScript frontend
│   ├── 📁 src/         # Source code
│   │   ├── 📁 components/ # UI components
│   │   └── 📁 store/    # State management
│   └── 📄 package.json # Node.js dependencies
├── 📄 docker-compose.yml # Docker configuration
├── 📄 .env.example     # Environment template
├── 📄 run-jarvis.bat   # Quick start script
└── 📄 README.md        # Full documentation
```

## 🔑 Essential API Keys

### Required (Free)
- **OpenAI** - https://platform.openai.com/api-keys (Free $5 credit)
- **Google Gemini** - https://makersuite.google.com/app/apikey (Free)

### Optional (Free Tiers)
- **Weather** - https://openweathermap.org/api (1000 calls/day)
- **News** - https://newsapi.org/ (1000 requests/day)
- **Supabase** - https://supabase.com/ (Cloud database)

## 🎮 How to Use

### Voice Commands
- "Hello JARVIS" - Start conversation
- "Turn on the lights" - Smart home control
- "What's the weather?" - Get weather info
- "Book dinner for tonight" - Make reservations
- "Analyze this image" - Computer vision
- "Check my health" - Health monitoring

### Web Interface
1. **Dashboard** - Overview and quick actions
2. **Voice** - Voice chat interface
3. **Vision** - Image analysis and object detection
4. **Smart Home** - Device and room control
5. **Healthcare** - Medical monitoring and emergency
6. **Booking** - Travel and appointment booking
7. **Automation** - Workflows and AI rules
8. **Settings** - Personalization and preferences

## 🚀 Deployment Options

### Local Development
```bash
run-jarvis.bat  # Windows
./run-jarvis.sh # Linux/Mac
```

### Docker
```bash
docker-compose up -d
```

### Cloud (Production)
- **Frontend**: Vercel, Netlify, AWS S3
- **Backend**: Railway, Heroku, AWS EC2
- **Database**: Supabase, PostgreSQL

## 🔧 Troubleshooting

### Backend Issues
```bash
cd backend
python --version  # Should be 3.11+
pip install -r requirements.txt
python main.py
```

### Frontend Issues
```bash
cd frontend
node --version  # Should be 18+
npm install
npm run dev
```

### Common Fixes
- **Port conflicts**: Change ports in docker-compose.yml
- **API key errors**: Check .env file formatting
- **Permission errors**: Run as administrator on Windows

## 📞 Support

- **Documentation**: README.md, DEPLOYMENT_GUIDE.md
- **Issues**: GitHub Issues
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🎉 Success!

If you see:
- ✅ Backend running on http://localhost:8000
- ✅ Frontend running on http://localhost:3000
- ✅ Login page loads with admin/admin123
- ✅ Voice interface responds to "Hello JARVIS"

**Congratulations! Your JARVIS AI Assistant is ready!**

---

### 🌟 Next Steps
1. Explore all features through the web interface
2. Try voice commands for hands-free control
3. Set up additional API keys for full functionality
4. Configure smart home devices
5. Create custom automation rules
6. Deploy to cloud for 24/7 access

**Welcome to the future of AI assistance! 🤖✨**
