import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  HealthAndSafety,
  Emergency,
  Medication,
  MonitorHeart,
  Psychology,
  LocalHospital,
  Warning,
  Phone,
  Add,
  Schedule,
} from '@mui/icons-material';
import axios from 'axios';

interface SymptomAnalysis {
  condition: string;
  probability: number;
  urgency: 'low' | 'medium' | 'high' | 'emergency';
  recommendations: string[];
  specialist: string;
}

interface VitalSigns {
  heartRate: number;
  bloodPressure: { systolic: number; diastolic: number };
  temperature: number;
  oxygenSaturation: number;
  timestamp: Date;
}

const Healthcare: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [symptoms, setSymptoms] = useState<string[]>([]);
  const [newSymptom, setNewSymptom] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<SymptomAnalysis | null>(null);
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [medications, setMedications] = useState([
    { name: 'Vitamin D', dosage: '1000 IU', frequency: 'Daily', time: '09:00' },
    { name: 'Omega-3', dosage: '500mg', frequency: 'Twice daily', time: '09:00, 21:00' },
  ]);
  const [vitalSigns, setVitalSigns] = useState<VitalSigns[]>([
    {
      heartRate: 72,
      bloodPressure: { systolic: 120, diastolic: 80 },
      temperature: 98.6,
      oxygenSaturation: 98,
      timestamp: new Date(),
    },
  ]);

  const addSymptom = () => {
    if (newSymptom.trim() && !symptoms.includes(newSymptom.trim())) {
      setSymptoms([...symptoms, newSymptom.trim()]);
      setNewSymptom('');
    }
  };

  const removeSymptom = (symptom: string) => {
    setSymptoms(symptoms.filter(s => s !== symptom));
  };

  const analyzeSymptoms = async () => {
    if (symptoms.length === 0) return;

    setIsAnalyzing(true);
    try {
      const response = await axios.post('/api/v1/healthcare/analyze-symptoms', {
        symptoms,
        age: parseInt(age),
        gender,
      });
      setAnalysis(response.data);
      
      if (response.data.urgency === 'emergency') {
        setEmergencyDialogOpen(true);
      }
    } catch (error) {
      console.error('Symptom analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const callEmergencyServices = () => {
    // In a real app, this would integrate with emergency services
    window.open('tel:911');
  };

  const SymptomAnalysisTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Symptom Checker
          </Typography>
          
          <Alert severity="warning" sx={{ mb: 3 }}>
            This is for informational purposes only. Always consult healthcare professionals for medical advice.
          </Alert>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Age"
                value={age}
                onChange={(e) => setAge(e.target.value)}
                type="number"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select value={gender} onChange={(e) => setGender(e.target.value)}>
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <TextField
              fullWidth
              label="Add symptom"
              value={newSymptom}
              onChange={(e) => setNewSymptom(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addSymptom()}
            />
            <Button onClick={addSymptom} startIcon={<Add />}>
              Add
            </Button>
          </Box>

          <Box sx={{ mb: 3 }}>
            {symptoms.map((symptom, index) => (
              <Chip
                key={index}
                label={symptom}
                onDelete={() => removeSymptom(symptom)}
                sx={{ mr: 1, mb: 1 }}
              />
            ))}
          </Box>

          <Button
            variant="contained"
            onClick={analyzeSymptoms}
            disabled={symptoms.length === 0 || isAnalyzing}
            fullWidth
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze Symptoms'}
          </Button>

          {isAnalyzing && <LinearProgress sx={{ mt: 2 }} />}
        </CardContent>
      </Card>

      {analysis && (
        <Card sx={{ bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Analysis Results
            </Typography>
            
            <Alert 
              severity={
                analysis.urgency === 'emergency' ? 'error' :
                analysis.urgency === 'high' ? 'warning' :
                analysis.urgency === 'medium' ? 'info' : 'success'
              }
              sx={{ mb: 2 }}
            >
              Urgency Level: {analysis.urgency.toUpperCase()}
            </Alert>

            <Typography variant="body1" gutterBottom>
              <strong>Possible Condition:</strong> {analysis.condition}
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>Probability:</strong> {(analysis.probability * 100).toFixed(1)}%
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>Recommended Specialist:</strong> {analysis.specialist}
            </Typography>

            <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
              Recommendations:
            </Typography>
            <List>
              {analysis.recommendations.map((rec, index) => (
                <ListItem key={index}>
                  <ListItemText primary={rec} />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}
    </Box>
  );

  const VitalSignsTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Vital Signs
          </Typography>
          
          {vitalSigns.length > 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: '#2a2a2a', textAlign: 'center' }}>
                  <CardContent>
                    <MonitorHeart color="error" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4">{vitalSigns[0].heartRate}</Typography>
                    <Typography variant="body2">BPM</Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: '#2a2a2a', textAlign: 'center' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>Blood Pressure</Typography>
                    <Typography variant="h4">
                      {vitalSigns[0].bloodPressure.systolic}/{vitalSigns[0].bloodPressure.diastolic}
                    </Typography>
                    <Typography variant="body2">mmHg</Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: '#2a2a2a', textAlign: 'center' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>Temperature</Typography>
                    <Typography variant="h4">{vitalSigns[0].temperature}</Typography>
                    <Typography variant="body2">°F</Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: '#2a2a2a', textAlign: 'center' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>Oxygen</Typography>
                    <Typography variant="h4">{vitalSigns[0].oxygenSaturation}%</Typography>
                    <Typography variant="body2">SpO2</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      <Card sx={{ bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Health Monitoring Features
          </Typography>
          <List>
            <ListItem>
              <ListItemIcon><MonitorHeart /></ListItemIcon>
              <ListItemText 
                primary="Heart Rate Monitoring" 
                secondary="Continuous tracking with alerts for abnormal readings"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><HealthAndSafety /></ListItemIcon>
              <ListItemText 
                primary="Blood Pressure Tracking" 
                secondary="Regular monitoring with trend analysis"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Psychology /></ListItemIcon>
              <ListItemText 
                primary="Mental Health Support" 
                secondary="Mood tracking and crisis intervention resources"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const MedicationsTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Medication Reminders
          </Typography>
          
          <List>
            {medications.map((med, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <Medication />
                </ListItemIcon>
                <ListItemText
                  primary={med.name}
                  secondary={`${med.dosage} - ${med.frequency} at ${med.time}`}
                />
              </ListItem>
            ))}
          </List>

          <Button variant="outlined" startIcon={<Add />} sx={{ mt: 2 }}>
            Add Medication
          </Button>
        </CardContent>
      </Card>

      <Card sx={{ bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Upcoming Reminders
          </Typography>
          <List>
            <ListItem>
              <ListItemIcon><Schedule /></ListItemIcon>
              <ListItemText 
                primary="Vitamin D - 1000 IU"
                secondary="Due in 2 hours (9:00 AM)"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Schedule /></ListItemIcon>
              <ListItemText 
                primary="Omega-3 - 500mg"
                secondary="Due in 2 hours (9:00 AM)"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const EmergencyTab = () => (
    <Box>
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Emergency Services
        </Typography>
        In case of emergency, call 911 immediately or use the button below.
      </Alert>

      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Button
            variant="contained"
            color="error"
            size="large"
            fullWidth
            startIcon={<Emergency />}
            onClick={callEmergencyServices}
            sx={{ mb: 2, py: 2 }}
          >
            CALL EMERGENCY SERVICES
          </Button>
          
          <Typography variant="body2" color="text.secondary" textAlign="center">
            This will dial 911 (or your local emergency number)
          </Typography>
        </CardContent>
      </Card>

      <Card sx={{ bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Emergency Contacts
          </Typography>
          <List>
            <ListItem>
              <ListItemIcon><Phone /></ListItemIcon>
              <ListItemText 
                primary="Emergency Services"
                secondary="911"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><LocalHospital /></ListItemIcon>
              <ListItemText 
                primary="Nearest Hospital"
                secondary="General Hospital - (*************"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Phone /></ListItemIcon>
              <ListItemText 
                primary="Primary Care Doctor"
                secondary="Dr. Smith - (*************"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const tabs = [
    { label: 'Symptoms', component: <SymptomAnalysisTab /> },
    { label: 'Vital Signs', component: <VitalSignsTab /> },
    { label: 'Medications', component: <MedicationsTab /> },
    { label: 'Emergency', component: <EmergencyTab /> },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Healthcare & Medical
      </Typography>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabs.map((tab, index) => (
          <Tab key={index} label={tab.label} />
        ))}
      </Tabs>

      {tabs[currentTab].component}

      {/* Emergency Dialog */}
      <Dialog open={emergencyDialogOpen} onClose={() => setEmergencyDialogOpen(false)}>
        <DialogTitle>
          <Warning color="error" sx={{ mr: 1 }} />
          Emergency Alert
        </DialogTitle>
        <DialogContent>
          <Typography>
            Your symptoms indicate a potential emergency. Please seek immediate medical attention.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialogOpen(false)}>
            Dismiss
          </Button>
          <Button onClick={callEmergencyServices} color="error" variant="contained">
            Call 911
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Healthcare;
