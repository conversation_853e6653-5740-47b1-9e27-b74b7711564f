import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Tabs,
  Tab,
  Paper,
} from '@mui/material';
import {
  Camera,
  Upload,
  Visibility,
  Face,
  TextFields,
  Psychology,
  ColorLens,
  Search,
  AutoAwesome,
} from '@mui/icons-material';
import axios from 'axios';

interface AnalysisResult {
  objects?: Array<{ name: string; confidence: number; bbox: number[] }>;
  faces?: Array<{ age: number; gender: string; emotion: string; confidence: number }>;
  text?: string;
  scene?: string;
  colors?: Array<{ color: string; percentage: number }>;
  quality?: { score: number; issues: string[] };
}

const ComputerVision: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);

  const analysisTypes = [
    { id: 'full', name: 'Full Analysis', icon: <AutoAwesome /> },
    { id: 'objects', name: 'Object Detection', icon: <Visibility /> },
    { id: 'faces', name: 'Face Recognition', icon: <Face /> },
    { id: 'text', name: 'Text Extraction', icon: <TextFields /> },
    { id: 'scene', name: 'Scene Understanding', icon: <Psychology /> },
    { id: 'colors', name: 'Color Analysis', icon: <ColorLens /> },
  ];

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      setAnalysisResult(null);
      setError('');
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsCameraActive(true);
      }
    } catch (err) {
      setError('Camera access denied or not available');
    }
  };

  const stopCamera = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      setIsCameraActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(video, 0, 0);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], 'camera-capture.jpg', { type: 'image/jpeg' });
          setSelectedImage(file);
          setImagePreview(canvas.toDataURL());
          stopCamera();
        }
      });
    }
  };

  const analyzeImage = async (analysisType: string = 'full') => {
    if (!selectedImage) return;

    setIsAnalyzing(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', selectedImage);
      formData.append('analysis_type', analysisType);

      const response = await axios.post('/api/v1/advanced-vision/analyze', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      setAnalysisResult(response.data);
    } catch (err) {
      setError('Analysis failed. Please try again.');
      console.error('Vision analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const ImageUploadTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Upload Image for Analysis
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button
              variant="outlined"
              startIcon={<Upload />}
              onClick={() => fileInputRef.current?.click()}
            >
              Choose Image
            </Button>
            <Button
              variant="outlined"
              startIcon={<Camera />}
              onClick={startCamera}
              disabled={isCameraActive}
            >
              Use Camera
            </Button>
          </Box>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            style={{ display: 'none' }}
          />

          {isCameraActive && (
            <Box sx={{ mb: 3 }}>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                style={{ width: '100%', maxWidth: 400, borderRadius: 8 }}
              />
              <Box sx={{ mt: 2 }}>
                <Button onClick={captureImage} variant="contained" sx={{ mr: 2 }}>
                  Capture
                </Button>
                <Button onClick={stopCamera} variant="outlined">
                  Cancel
                </Button>
              </Box>
            </Box>
          )}

          {imagePreview && (
            <Box sx={{ mb: 3 }}>
              <img
                src={imagePreview}
                alt="Preview"
                style={{ width: '100%', maxWidth: 400, borderRadius: 8 }}
              />
            </Box>
          )}

          <canvas ref={canvasRef} style={{ display: 'none' }} />
        </CardContent>
      </Card>

      {selectedImage && (
        <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Analysis Options
            </Typography>
            <Grid container spacing={2}>
              {analysisTypes.map((type) => (
                <Grid item xs={12} sm={6} md={4} key={type.id}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={type.icon}
                    onClick={() => analyzeImage(type.id)}
                    disabled={isAnalyzing}
                  >
                    {type.name}
                  </Button>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );

  const ResultsTab = () => (
    <Box>
      {isAnalyzing && (
        <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <CircularProgress sx={{ mb: 2 }} />
            <Typography>Analyzing image...</Typography>
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {analysisResult && (
        <Grid container spacing={3}>
          {analysisResult.objects && (
            <Grid item xs={12} md={6}>
              <Card sx={{ bgcolor: '#1a1a1a' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Objects Detected
                  </Typography>
                  <List>
                    {analysisResult.objects.map((obj, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={obj.name}
                          secondary={`Confidence: ${(obj.confidence * 100).toFixed(1)}%`}
                        />
                        <Chip
                          label={`${(obj.confidence * 100).toFixed(0)}%`}
                          color={obj.confidence > 0.8 ? 'success' : obj.confidence > 0.5 ? 'warning' : 'error'}
                          size="small"
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}

          {analysisResult.faces && (
            <Grid item xs={12} md={6}>
              <Card sx={{ bgcolor: '#1a1a1a' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Face Analysis
                  </Typography>
                  <List>
                    {analysisResult.faces.map((face, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={`Person ${index + 1}`}
                          secondary={`Age: ~${face.age}, Gender: ${face.gender}, Emotion: ${face.emotion}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}

          {analysisResult.text && (
            <Grid item xs={12}>
              <Card sx={{ bgcolor: '#1a1a1a' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Extracted Text
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: '#2a2a2a' }}>
                    <Typography variant="body2">
                      {analysisResult.text}
                    </Typography>
                  </Paper>
                </CardContent>
              </Card>
            </Grid>
          )}

          {analysisResult.scene && (
            <Grid item xs={12}>
              <Card sx={{ bgcolor: '#1a1a1a' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Scene Description
                  </Typography>
                  <Typography variant="body1">
                    {analysisResult.scene}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          {analysisResult.colors && (
            <Grid item xs={12} md={6}>
              <Card sx={{ bgcolor: '#1a1a1a' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Color Analysis
                  </Typography>
                  <List>
                    {analysisResult.colors.map((color, index) => (
                      <ListItem key={index}>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            bgcolor: color.color,
                            borderRadius: 1,
                            mr: 2,
                          }}
                        />
                        <ListItemText
                          primary={color.color}
                          secondary={`${color.percentage.toFixed(1)}%`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );

  const tabs = [
    { label: 'Upload & Analyze', component: <ImageUploadTab /> },
    { label: 'Results', component: <ResultsTab /> },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Computer Vision
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Upload an image or use your camera to analyze objects, faces, text, and more using advanced AI models.
      </Alert>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabs.map((tab, index) => (
          <Tab key={index} label={tab.label} />
        ))}
      </Tabs>

      {tabs[currentTab].component}

      {/* Capabilities Info */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Vision Capabilities
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label="Object Detection (80+ classes)" size="small" />
            <Chip label="Face Recognition" size="small" />
            <Chip label="Emotion Detection" size="small" />
            <Chip label="Age & Gender Estimation" size="small" />
            <Chip label="OCR (100+ languages)" size="small" />
            <Chip label="Scene Understanding" size="small" />
            <Chip label="Color Analysis" size="small" />
            <Chip label="Quality Assessment" size="small" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ComputerVision;
