"""
Ultra-Advanced Voice Processing Service for JARVIS
Features: Wake word detection, voice cloning, emotion analysis, multi-language support
"""

import asyncio
import io
import wave
import tempfile
import os
import threading
import time
import json
from typing import Dict, List, Any, Optional, Union, Tuple
import speech_recognition as sr
import whisper
from gtts import gTTS
import pygame
from pydub import AudioSegment
import numpy as np
import librosa
import soundfile as sf
from scipy.signal import butter, filtfilt
import webrtcvad
import collections
import contextlib
import sys
import pyaudio
from loguru import logger

from ..core.config import settings

# Advanced imports for wake word and emotion detection
try:
    import pvporcupine
    import pvrhino
    PICOVOICE_AVAILABLE = True
except ImportError:
    PICOVOICE_AVAILABLE = False
    logger.warning("Picovoice not available. Wake word detection will use basic implementation.")

try:
    import torch
    import torchaudio
    from transformers import Wav2Vec2ForSequenceClassification, Wav2Vec2FeatureExtractor
    EMOTION_DETECTION_AVAILABLE = True
except ImportError:
    EMOTION_DETECTION_AVAILABLE = False
    logger.warning("Emotion detection models not available.")

try:
    from TTS.api import TTS
    ADVANCED_TTS_AVAILABLE = True
except ImportError:
    ADVANCED_TTS_AVAILABLE = False
    logger.warning("Advanced TTS not available. Using basic TTS.")

# Removed Azure Speech dependency - using Whisper instead
AZURE_SPEECH_AVAILABLE = False

class UltraAdvancedVoiceService:
    """Ultra-Advanced voice processing service with futuristic capabilities"""

    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.whisper_model = None
        self.supported_languages = [
            'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
            'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl', 'sv', 'da', 'no'
        ]
        self.voice_profiles = {}

        # Advanced features
        self.wake_words = ["jarvis", "hey jarvis", "ok jarvis", "computer", "assistant"]
        self.current_language = "en-US"
        self.emotion_model = None
        self.voice_cloning_model = None
        self.conversation_context = []
        self.is_wake_word_active = False
        self.wake_word_thread = None
        self.voice_activity_detector = None

        # Audio processing settings
        self.sample_rate = 16000
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)

        # Voice characteristics for cloning
        self.voice_characteristics = {
            "pitch": 1.0,
            "speed": 1.0,
            "emotion": "neutral",
            "accent": "american",
            "gender": "female"
        }

        # Real-time processing
        self.audio_buffer = collections.deque(maxlen=100)
        self.processing_thread = None
        self.is_processing = False
        
    async def initialize(self):
        """Initialize ultra-advanced voice service"""
        try:
            # Initialize pygame mixer for audio playback
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

            # Load Whisper model for advanced speech recognition
            self.whisper_model = whisper.load_model("base")

            # Initialize voice activity detector
            try:
                self.voice_activity_detector = webrtcvad.Vad(3)  # Aggressiveness level 3
            except Exception as e:
                logger.warning(f"VAD initialization failed: {e}")

            # Initialize advanced features
            await self._initialize_advanced_features()

            # Initialize microphone
            try:
                self.microphone = sr.Microphone()
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=2)
                    self.recognizer.energy_threshold = 300
                    self.recognizer.dynamic_energy_threshold = True
                    self.recognizer.pause_threshold = 0.8
                    self.recognizer.phrase_threshold = 0.3
            except Exception as e:
                logger.warning(f"Microphone initialization failed: {e}")

            # Start wake word detection
            await self.start_wake_word_detection()

            logger.info("Ultra-advanced voice service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize voice service: {e}")
            raise

    async def _initialize_advanced_features(self):
        """Initialize advanced voice processing features"""
        try:
            # Initialize emotion detection model
            if EMOTION_DETECTION_AVAILABLE:
                try:
                    self.emotion_feature_extractor = Wav2Vec2FeatureExtractor.from_pretrained(
                        "facebook/wav2vec2-base-960h"
                    )
                    # Note: You would need to train or find a pre-trained emotion model
                    logger.info("Emotion detection initialized")
                except Exception as e:
                    logger.warning(f"Emotion detection initialization failed: {e}")

            # Initialize advanced TTS
            if ADVANCED_TTS_AVAILABLE:
                try:
                    self.advanced_tts = TTS("tts_models/en/ljspeech/tacotron2-DDC")
                    logger.info("Advanced TTS initialized")
                except Exception as e:
                    logger.warning(f"Advanced TTS initialization failed: {e}")

            # Initialize wake word detection
            if PICOVOICE_AVAILABLE:
                try:
                    # You would need Picovoice access key
                    # self.porcupine = pvporcupine.create(keywords=["jarvis"])
                    logger.info("Wake word detection ready")
                except Exception as e:
                    logger.warning(f"Wake word detection initialization failed: {e}")

            # Initialize Azure Speech Services if available
            if AZURE_SPEECH_AVAILABLE:
                try:
                    # Configure Azure Speech
                    speech_key = settings.AZURE_SPEECH_KEY if hasattr(settings, 'AZURE_SPEECH_KEY') else None
                    service_region = settings.AZURE_SPEECH_REGION if hasattr(settings, 'AZURE_SPEECH_REGION') else None

                    if speech_key and service_region:
                        speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)
                        self.azure_speech_config = speech_config
                        logger.info("Azure Speech Services initialized")
                except Exception as e:
                    logger.warning(f"Azure Speech Services initialization failed: {e}")

        except Exception as e:
            logger.error(f"Error initializing advanced features: {e}")

    async def start_wake_word_detection(self):
        """Start wake word detection in background"""
        try:
            if not self.is_wake_word_active:
                self.is_wake_word_active = True
                self.wake_word_thread = threading.Thread(target=self._wake_word_detection_loop, daemon=True)
                self.wake_word_thread.start()
                logger.info("Wake word detection started")
        except Exception as e:
            logger.error(f"Failed to start wake word detection: {e}")

    def _wake_word_detection_loop(self):
        """Continuous wake word detection loop"""
        try:
            while self.is_wake_word_active:
                try:
                    if self.microphone:
                        with self.microphone as source:
                            # Listen for audio
                            audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=3)

                            # Convert to text
                            try:
                                text = self.recognizer.recognize_google(audio, language=self.current_language)
                                text_lower = text.lower()

                                # Check for wake words
                                for wake_word in self.wake_words:
                                    if wake_word in text_lower:
                                        logger.info(f"Wake word detected: {wake_word}")
                                        # Trigger wake word callback
                                        asyncio.create_task(self._on_wake_word_detected(text))
                                        break

                            except sr.UnknownValueError:
                                pass  # No speech detected
                            except sr.RequestError:
                                pass  # API error

                except Exception as e:
                    if self.is_wake_word_active:
                        logger.debug(f"Wake word detection error: {e}")

                time.sleep(0.1)  # Small delay to prevent excessive CPU usage

        except Exception as e:
            logger.error(f"Wake word detection loop error: {e}")

    async def _on_wake_word_detected(self, text: str):
        """Handle wake word detection"""
        try:
            logger.info(f"Processing wake word command: {text}")
            # Here you would integrate with your AI service
            # For now, just log the detection

            # Play acknowledgment sound
            await self.play_acknowledgment_sound()

        except Exception as e:
            logger.error(f"Wake word handling error: {e}")

    async def play_acknowledgment_sound(self):
        """Play a sound to acknowledge wake word detection"""
        try:
            # Generate a simple beep sound
            duration = 0.2  # seconds
            sample_rate = 22050
            frequency = 800  # Hz

            t = np.linspace(0, duration, int(sample_rate * duration))
            beep = np.sin(2 * np.pi * frequency * t) * 0.3

            # Convert to audio format
            audio_data = (beep * 32767).astype(np.int16)

            # Play the beep
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                sf.write(temp_file.name, audio_data, sample_rate)
                pygame.mixer.music.load(temp_file.name)
                pygame.mixer.music.play()

                # Wait for playback
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.01)

                os.unlink(temp_file.name)

        except Exception as e:
            logger.error(f"Acknowledgment sound error: {e}")

    async def detect_emotion(self, audio_data: bytes) -> Dict[str, Any]:
        """Detect emotion from voice audio"""
        try:
            if not EMOTION_DETECTION_AVAILABLE:
                return {"emotion": "neutral", "confidence": 0.0, "method": "unavailable"}

            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            # Load audio for analysis
            audio, sr_rate = librosa.load(temp_file_path, sr=16000)

            # Basic emotion detection using audio features
            # This is a simplified implementation - in practice you'd use a trained model
            features = self._extract_emotion_features(audio, sr_rate)
            emotion = self._classify_emotion(features)

            # Clean up
            os.unlink(temp_file_path)

            return emotion

        except Exception as e:
            logger.error(f"Emotion detection error: {e}")
            return {"emotion": "neutral", "confidence": 0.0, "error": str(e)}

    def _extract_emotion_features(self, audio: np.ndarray, sample_rate: int) -> Dict[str, float]:
        """Extract features for emotion detection"""
        try:
            features = {}

            # Spectral features
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sample_rate)[0]
            features['spectral_centroid_mean'] = np.mean(spectral_centroids)
            features['spectral_centroid_std'] = np.std(spectral_centroids)

            # MFCC features
            mfccs = librosa.feature.mfcc(y=audio, sr=sample_rate, n_mfcc=13)
            features['mfcc_mean'] = np.mean(mfccs)
            features['mfcc_std'] = np.std(mfccs)

            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(audio)[0]
            features['zcr_mean'] = np.mean(zcr)
            features['zcr_std'] = np.std(zcr)

            # Chroma features
            chroma = librosa.feature.chroma_stft(y=audio, sr=sample_rate)
            features['chroma_mean'] = np.mean(chroma)
            features['chroma_std'] = np.std(chroma)

            # Tempo
            tempo, _ = librosa.beat.beat_track(y=audio, sr=sample_rate)
            features['tempo'] = tempo

            return features

        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            return {}

    def _classify_emotion(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Classify emotion based on features (simplified implementation)"""
        try:
            if not features:
                return {"emotion": "neutral", "confidence": 0.0}

            # Simplified emotion classification based on audio features
            # In practice, you'd use a trained machine learning model

            spectral_centroid = features.get('spectral_centroid_mean', 0)
            zcr = features.get('zcr_mean', 0)
            tempo = features.get('tempo', 120)

            # Simple heuristic-based classification
            if spectral_centroid > 2000 and zcr > 0.1:
                emotion = "excited"
                confidence = 0.7
            elif spectral_centroid < 1000 and tempo < 100:
                emotion = "sad"
                confidence = 0.6
            elif zcr > 0.15:
                emotion = "angry"
                confidence = 0.65
            elif tempo > 140:
                emotion = "happy"
                confidence = 0.6
            else:
                emotion = "neutral"
                confidence = 0.8

            return {
                "emotion": emotion,
                "confidence": confidence,
                "features": features,
                "method": "heuristic"
            }

        except Exception as e:
            logger.error(f"Emotion classification error: {e}")
            return {"emotion": "neutral", "confidence": 0.0, "error": str(e)}

    async def speech_to_text(
        self,
        audio_data: Union[bytes, str],
        language: str = "en",
        use_whisper: bool = True
    ) -> Dict[str, Any]:
        """Convert speech to text using multiple recognition engines"""
        try:
            if isinstance(audio_data, str):
                # Audio file path
                audio_file = audio_data
            else:
                # Audio bytes - save to temporary file
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    temp_file.write(audio_data)
                    audio_file = temp_file.name
            
            results = {}
            
            # Use Whisper for high-quality recognition
            if use_whisper and self.whisper_model:
                try:
                    whisper_result = self.whisper_model.transcribe(
                        audio_file,
                        language=language if language != "auto" else None
                    )
                    results["whisper"] = {
                        "text": whisper_result["text"].strip(),
                        "confidence": 0.95,  # Whisper doesn't provide confidence
                        "language": whisper_result.get("language", language)
                    }
                except Exception as e:
                    logger.error(f"Whisper recognition failed: {e}")
            
            # Use Google Speech Recognition as fallback
            try:
                with sr.AudioFile(audio_file) as source:
                    audio = self.recognizer.record(source)
                
                google_text = self.recognizer.recognize_google(
                    audio,
                    language=language if language != "auto" else None
                )
                
                results["google"] = {
                    "text": google_text,
                    "confidence": 0.85,
                    "language": language
                }
            except Exception as e:
                logger.error(f"Google recognition failed: {e}")
            
            # Clean up temporary file
            if isinstance(audio_data, bytes) and os.path.exists(audio_file):
                os.unlink(audio_file)
            
            # Return best result
            if "whisper" in results:
                return results["whisper"]
            elif "google" in results:
                return results["google"]
            else:
                return {"text": "", "confidence": 0.0, "language": language}
                
        except Exception as e:
            logger.error(f"Speech recognition error: {e}")
            return {"text": "", "confidence": 0.0, "language": language, "error": str(e)}
    
    async def text_to_speech(
        self,
        text: str,
        language: str = "en",
        voice_speed: float = 1.0,
        voice_pitch: float = 1.0,
        emotion: str = "neutral",
        voice_profile: Optional[str] = None
    ) -> bytes:
        """Ultra-advanced text to speech with emotion and voice cloning"""
        try:
            # Try advanced TTS first
            if ADVANCED_TTS_AVAILABLE and hasattr(self, 'advanced_tts'):
                try:
                    return await self._advanced_text_to_speech(
                        text, language, voice_speed, voice_pitch, emotion, voice_profile
                    )
                except Exception as e:
                    logger.warning(f"Advanced TTS failed, falling back to basic: {e}")

            # Try Azure Speech Services
            if AZURE_SPEECH_AVAILABLE and hasattr(self, 'azure_speech_config'):
                try:
                    return await self._azure_text_to_speech(
                        text, language, voice_speed, voice_pitch, emotion
                    )
                except Exception as e:
                    logger.warning(f"Azure TTS failed, falling back to basic: {e}")

            # Fallback to enhanced gTTS
            return await self._enhanced_gtts(text, language, voice_speed, voice_pitch, emotion)

        except Exception as e:
            logger.error(f"Text-to-speech error: {e}")
            return b""

    async def _advanced_text_to_speech(
        self,
        text: str,
        language: str,
        voice_speed: float,
        voice_pitch: float,
        emotion: str,
        voice_profile: Optional[str]
    ) -> bytes:
        """Advanced TTS using Coqui TTS"""
        try:
            # Generate speech with advanced TTS
            wav = self.advanced_tts.tts(text=text, language=language)

            # Apply voice modifications
            wav = self._apply_voice_effects(wav, voice_speed, voice_pitch, emotion)

            # Convert to bytes
            wav_io = io.BytesIO()
            sf.write(wav_io, wav, 22050, format='WAV')
            return wav_io.getvalue()

        except Exception as e:
            logger.error(f"Advanced TTS error: {e}")
            raise

    async def _azure_text_to_speech(
        self,
        text: str,
        language: str,
        voice_speed: float,
        voice_pitch: float,
        emotion: str
    ) -> bytes:
        """Azure Speech Services TTS"""
        try:
            # Configure voice based on language and emotion
            voice_name = self._get_azure_voice_name(language, emotion)

            # Create SSML with emotion and prosody
            ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="{language}">
                <voice name="{voice_name}">
                    <prosody rate="{voice_speed}" pitch="{voice_pitch:+.0%}">
                        <mstts:express-as style="{emotion}">
                            {text}
                        </mstts:express-as>
                    </prosody>
                </voice>
            </speak>
            """

            # Create synthesizer
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=self.azure_speech_config)

            # Synthesize speech
            result = synthesizer.speak_ssml_async(ssml).get()

            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return result.audio_data
            else:
                raise Exception(f"Azure TTS failed: {result.reason}")

        except Exception as e:
            logger.error(f"Azure TTS error: {e}")
            raise

    async def _enhanced_gtts(
        self,
        text: str,
        language: str,
        voice_speed: float,
        voice_pitch: float,
        emotion: str
    ) -> bytes:
        """Enhanced gTTS with post-processing"""
        try:
            # Use gTTS for basic synthesis
            tts = gTTS(text=text, lang=language, slow=False)

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                tts.save(temp_file.name)
                temp_file_path = temp_file.name

            # Load and process audio
            audio = AudioSegment.from_mp3(temp_file_path)

            # Apply voice effects
            audio = self._apply_audio_effects(audio, voice_speed, voice_pitch, emotion)

            # Convert to WAV bytes
            wav_io = io.BytesIO()
            audio.export(wav_io, format="wav")
            wav_bytes = wav_io.getvalue()

            # Clean up
            os.unlink(temp_file_path)

            return wav_bytes

        except Exception as e:
            logger.error(f"Enhanced gTTS error: {e}")
            return b""

    def _apply_voice_effects(self, wav: np.ndarray, speed: float, pitch: float, emotion: str) -> np.ndarray:
        """Apply voice effects to audio array"""
        try:
            # Apply speed change
            if speed != 1.0:
                wav = librosa.effects.time_stretch(wav, rate=speed)

            # Apply pitch shift
            if pitch != 1.0:
                pitch_steps = 12 * np.log2(pitch)  # Convert to semitones
                wav = librosa.effects.pitch_shift(wav, sr=22050, n_steps=pitch_steps)

            # Apply emotion-based effects
            wav = self._apply_emotion_effects(wav, emotion)

            return wav

        except Exception as e:
            logger.error(f"Voice effects error: {e}")
            return wav

    def _apply_audio_effects(self, audio: AudioSegment, speed: float, pitch: float, emotion: str) -> AudioSegment:
        """Apply audio effects to AudioSegment"""
        try:
            # Apply speed change
            if speed != 1.0:
                audio = audio.speedup(playback_speed=speed)

            # Apply pitch shift (simplified)
            if pitch != 1.0:
                # Convert pitch ratio to octaves
                octaves = np.log2(pitch)
                new_sample_rate = int(audio.frame_rate * (2 ** octaves))
                audio = audio._spawn(audio.raw_data, overrides={"frame_rate": new_sample_rate})
                audio = audio.set_frame_rate(audio.frame_rate)

            # Apply emotion-based effects
            audio = self._apply_emotion_effects_audio(audio, emotion)

            return audio

        except Exception as e:
            logger.error(f"Audio effects error: {e}")
            return audio

    def _apply_emotion_effects(self, wav: np.ndarray, emotion: str) -> np.ndarray:
        """Apply emotion-specific effects to audio"""
        try:
            if emotion == "happy":
                # Slightly increase pitch and add brightness
                wav = librosa.effects.pitch_shift(wav, sr=22050, n_steps=1)
            elif emotion == "sad":
                # Decrease pitch and add reverb effect
                wav = librosa.effects.pitch_shift(wav, sr=22050, n_steps=-2)
            elif emotion == "angry":
                # Increase intensity and add distortion
                wav = wav * 1.2
                wav = np.clip(wav, -1.0, 1.0)
            elif emotion == "excited":
                # Increase speed and pitch
                wav = librosa.effects.time_stretch(wav, rate=1.1)
                wav = librosa.effects.pitch_shift(wav, sr=22050, n_steps=2)

            return wav

        except Exception as e:
            logger.error(f"Emotion effects error: {e}")
            return wav

    def _apply_emotion_effects_audio(self, audio: AudioSegment, emotion: str) -> AudioSegment:
        """Apply emotion-specific effects to AudioSegment"""
        try:
            if emotion == "happy":
                # Increase volume and brightness
                audio = audio + 2  # Increase volume by 2dB
            elif emotion == "sad":
                # Decrease volume and add low-pass filter effect
                audio = audio - 3  # Decrease volume by 3dB
            elif emotion == "angry":
                # Increase volume and add distortion
                audio = audio + 5
            elif emotion == "excited":
                # Increase volume and speed
                audio = audio + 3
                audio = audio.speedup(playback_speed=1.1)

            return audio

        except Exception as e:
            logger.error(f"Audio emotion effects error: {e}")
            return audio

    def _get_azure_voice_name(self, language: str, emotion: str) -> str:
        """Get appropriate Azure voice name based on language and emotion"""
        voice_map = {
            "en": {
                "neutral": "en-US-JennyNeural",
                "happy": "en-US-AriaNeural",
                "sad": "en-US-GuyNeural",
                "angry": "en-US-DavisNeural",
                "excited": "en-US-JaneNeural"
            },
            "es": {
                "neutral": "es-ES-ElviraNeural",
                "happy": "es-ES-AlvaroNeural"
            },
            "fr": {
                "neutral": "fr-FR-DeniseNeural",
                "happy": "fr-FR-HenriNeural"
            }
        }

        lang_voices = voice_map.get(language, voice_map["en"])
        return lang_voices.get(emotion, lang_voices.get("neutral", "en-US-JennyNeural"))

    async def clone_voice(self, user_id: str, reference_audio: List[bytes], target_text: str) -> bytes:
        """Clone voice from reference audio samples"""
        try:
            if len(reference_audio) < 3:
                raise ValueError("At least 3 reference audio samples required for voice cloning")

            # Analyze reference audio to extract voice characteristics
            voice_features = []
            for audio_data in reference_audio:
                features = await self._extract_voice_features(audio_data)
                if features:
                    voice_features.append(features)

            if not voice_features:
                raise ValueError("No valid voice features extracted")

            # Create voice profile
            voice_profile = self._create_voice_profile(voice_features)

            # Store voice profile
            self.voice_profiles[f"{user_id}_clone"] = voice_profile

            # Generate speech with cloned voice
            cloned_audio = await self._synthesize_with_cloned_voice(target_text, voice_profile)

            logger.info(f"Voice cloned successfully for user {user_id}")
            return cloned_audio

        except Exception as e:
            logger.error(f"Voice cloning error: {e}")
            return b""

    async def _extract_voice_features(self, audio_data: bytes) -> Optional[Dict[str, Any]]:
        """Extract detailed voice features for cloning"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            # Load audio
            audio, sr = librosa.load(temp_file_path, sr=22050)

            # Extract comprehensive features
            features = {
                # Fundamental frequency (pitch)
                "f0_mean": np.mean(librosa.yin(audio, fmin=50, fmax=400)),
                "f0_std": np.std(librosa.yin(audio, fmin=50, fmax=400)),

                # Spectral features
                "spectral_centroid": np.mean(librosa.feature.spectral_centroid(y=audio, sr=sr)),
                "spectral_bandwidth": np.mean(librosa.feature.spectral_bandwidth(y=audio, sr=sr)),
                "spectral_rolloff": np.mean(librosa.feature.spectral_rolloff(y=audio, sr=sr)),

                # MFCC features (voice timbre)
                "mfcc": np.mean(librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13), axis=1).tolist(),

                # Formant frequencies (approximation)
                "formants": self._estimate_formants(audio, sr),

                # Voice quality features
                "zcr": np.mean(librosa.feature.zero_crossing_rate(audio)),
                "energy": np.mean(librosa.feature.rms(y=audio)),

                # Temporal features
                "tempo": librosa.beat.tempo(y=audio, sr=sr)[0],
                "rhythm": self._extract_rhythm_features(audio, sr)
            }

            # Clean up
            os.unlink(temp_file_path)

            return features

        except Exception as e:
            logger.error(f"Voice feature extraction error: {e}")
            return None

    def _estimate_formants(self, audio: np.ndarray, sr: int) -> List[float]:
        """Estimate formant frequencies"""
        try:
            # Simple formant estimation using spectral peaks
            fft = np.abs(np.fft.fft(audio))
            freqs = np.fft.fftfreq(len(fft), 1/sr)

            # Find peaks in the spectrum
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(fft[:len(fft)//2], height=np.max(fft)*0.1)

            # Get the first 4 formants (typical for voice)
            formant_freqs = freqs[peaks][:4]

            # Fill with zeros if not enough formants found
            while len(formant_freqs) < 4:
                formant_freqs = np.append(formant_freqs, 0)

            return formant_freqs.tolist()

        except Exception as e:
            logger.error(f"Formant estimation error: {e}")
            return [0, 0, 0, 0]

    def _extract_rhythm_features(self, audio: np.ndarray, sr: int) -> Dict[str, float]:
        """Extract rhythm and timing features"""
        try:
            # Onset detection
            onset_frames = librosa.onset.onset_detect(y=audio, sr=sr)
            onset_times = librosa.frames_to_time(onset_frames, sr=sr)

            # Calculate rhythm features
            if len(onset_times) > 1:
                inter_onset_intervals = np.diff(onset_times)
                rhythm_features = {
                    "avg_ioi": np.mean(inter_onset_intervals),
                    "std_ioi": np.std(inter_onset_intervals),
                    "onset_rate": len(onset_times) / (len(audio) / sr)
                }
            else:
                rhythm_features = {"avg_ioi": 0, "std_ioi": 0, "onset_rate": 0}

            return rhythm_features

        except Exception as e:
            logger.error(f"Rhythm feature extraction error: {e}")
            return {"avg_ioi": 0, "std_ioi": 0, "onset_rate": 0}

    def _create_voice_profile(self, voice_features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a voice profile from multiple feature sets"""
        try:
            profile = {
                "sample_count": len(voice_features),
                "created_at": time.time()
            }

            # Average numerical features
            numerical_features = [
                "f0_mean", "f0_std", "spectral_centroid", "spectral_bandwidth",
                "spectral_rolloff", "zcr", "energy", "tempo"
            ]

            for feature in numerical_features:
                values = [f[feature] for f in voice_features if feature in f and not np.isnan(f[feature])]
                if values:
                    profile[feature] = np.mean(values)
                    profile[f"{feature}_std"] = np.std(values)

            # Average MFCC features
            mfcc_arrays = [f["mfcc"] for f in voice_features if "mfcc" in f]
            if mfcc_arrays:
                profile["mfcc"] = np.mean(mfcc_arrays, axis=0).tolist()

            # Average formants
            formant_arrays = [f["formants"] for f in voice_features if "formants" in f]
            if formant_arrays:
                profile["formants"] = np.mean(formant_arrays, axis=0).tolist()

            # Average rhythm features
            rhythm_features = [f["rhythm"] for f in voice_features if "rhythm" in f]
            if rhythm_features:
                for key in ["avg_ioi", "std_ioi", "onset_rate"]:
                    values = [r[key] for r in rhythm_features if key in r]
                    if values:
                        profile[f"rhythm_{key}"] = np.mean(values)

            return profile

        except Exception as e:
            logger.error(f"Voice profile creation error: {e}")
            return {}

    async def _synthesize_with_cloned_voice(self, text: str, voice_profile: Dict[str, Any]) -> bytes:
        """Synthesize speech using cloned voice characteristics"""
        try:
            # Generate base speech
            base_audio = await self._enhanced_gtts(text, "en", 1.0, 1.0, "neutral")

            # Apply voice profile characteristics
            modified_audio = await self._apply_voice_profile(base_audio, voice_profile)

            return modified_audio

        except Exception as e:
            logger.error(f"Cloned voice synthesis error: {e}")
            return b""

    async def _apply_voice_profile(self, audio_data: bytes, voice_profile: Dict[str, Any]) -> bytes:
        """Apply voice profile characteristics to audio"""
        try:
            # Load audio
            audio = AudioSegment.from_wav(io.BytesIO(audio_data))

            # Convert to numpy array for processing
            samples = np.array(audio.get_array_of_samples(), dtype=np.float32)
            if audio.channels == 2:
                samples = samples.reshape((-1, 2)).mean(axis=1)

            # Normalize
            samples = samples / np.max(np.abs(samples))

            # Apply pitch modification based on f0_mean
            if "f0_mean" in voice_profile and not np.isnan(voice_profile["f0_mean"]):
                target_pitch = voice_profile["f0_mean"]
                # Simple pitch shifting (in practice, you'd use more sophisticated methods)
                pitch_ratio = target_pitch / 150.0  # Assume 150Hz as baseline
                pitch_ratio = np.clip(pitch_ratio, 0.5, 2.0)  # Reasonable limits

                samples = librosa.effects.pitch_shift(samples, sr=audio.frame_rate, n_steps=12*np.log2(pitch_ratio))

            # Apply spectral modifications
            if "spectral_centroid" in voice_profile:
                # This is a simplified approach - real voice cloning would be much more complex
                pass

            # Convert back to audio
            samples = (samples * 32767).astype(np.int16)

            # Create new audio segment
            modified_audio = AudioSegment(
                samples.tobytes(),
                frame_rate=audio.frame_rate,
                sample_width=2,
                channels=1
            )

            # Export to bytes
            output = io.BytesIO()
            modified_audio.export(output, format="wav")

            return output.getvalue()

        except Exception as e:
            logger.error(f"Voice profile application error: {e}")
            return audio_data

    async def start_real_time_processing(self):
        """Start real-time voice processing"""
        try:
            if not self.is_processing:
                self.is_processing = True
                self.processing_thread = threading.Thread(target=self._real_time_processing_loop, daemon=True)
                self.processing_thread.start()
                logger.info("Real-time voice processing started")
        except Exception as e:
            logger.error(f"Failed to start real-time processing: {e}")

    def _real_time_processing_loop(self):
        """Real-time voice processing loop"""
        try:
            # Initialize PyAudio for real-time processing
            p = pyaudio.PyAudio()

            stream = p.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.frame_size
            )

            while self.is_processing:
                try:
                    # Read audio frame
                    audio_frame = stream.read(self.frame_size, exception_on_overflow=False)

                    # Add to buffer
                    self.audio_buffer.append(audio_frame)

                    # Process if we have enough frames
                    if len(self.audio_buffer) >= 10:  # Process every 300ms
                        combined_audio = b''.join(list(self.audio_buffer))

                        # Voice activity detection
                        if self.voice_activity_detector:
                            is_speech = self.voice_activity_detector.is_speech(
                                combined_audio[:self.frame_size], self.sample_rate
                            )

                            if is_speech:
                                # Process speech in background
                                asyncio.create_task(self._process_real_time_speech(combined_audio))

                        # Clear buffer
                        self.audio_buffer.clear()

                except Exception as e:
                    if self.is_processing:
                        logger.debug(f"Real-time processing error: {e}")

            # Cleanup
            stream.stop_stream()
            stream.close()
            p.terminate()

        except Exception as e:
            logger.error(f"Real-time processing loop error: {e}")

    async def _process_real_time_speech(self, audio_data: bytes):
        """Process real-time speech data"""
        try:
            # This is where you'd integrate with your AI service for real-time responses
            # For now, just log that speech was detected
            logger.debug("Real-time speech detected and processed")

        except Exception as e:
            logger.error(f"Real-time speech processing error: {e}")

    async def stop_real_time_processing(self):
        """Stop real-time voice processing"""
        try:
            self.is_processing = False
            if self.processing_thread:
                self.processing_thread.join(timeout=2)
            logger.info("Real-time voice processing stopped")
        except Exception as e:
            logger.error(f"Error stopping real-time processing: {e}")

    async def stop_wake_word_detection(self):
        """Stop wake word detection"""
        try:
            self.is_wake_word_active = False
            if self.wake_word_thread:
                self.wake_word_thread.join(timeout=2)
            logger.info("Wake word detection stopped")
        except Exception as e:
            logger.error(f"Error stopping wake word detection: {e}")

    async def play_audio(self, audio_data: bytes) -> bool:
        """Play audio data"""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Play using pygame
            pygame.mixer.music.load(temp_file_path)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)
            
            # Clean up
            os.unlink(temp_file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
            return False
    
    async def record_audio(self, duration: float = 5.0) -> Optional[bytes]:
        """Record audio from microphone"""
        try:
            if not self.microphone:
                logger.error("Microphone not available")
                return None
            
            with self.microphone as source:
                logger.info(f"Recording audio for {duration} seconds...")
                audio = self.recognizer.listen(source, timeout=duration, phrase_time_limit=duration)
            
            # Convert to WAV bytes
            wav_data = audio.get_wav_data()
            return wav_data
            
        except Exception as e:
            logger.error(f"Audio recording error: {e}")
            return None
    
    async def analyze_voice(self, audio_data: bytes) -> Dict[str, Any]:
        """Analyze voice characteristics"""
        try:
            # Save to temporary file for analysis
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Load audio for analysis
            audio = AudioSegment.from_wav(temp_file_path)
            
            # Basic audio analysis
            analysis = {
                "duration": len(audio) / 1000.0,  # seconds
                "sample_rate": audio.frame_rate,
                "channels": audio.channels,
                "loudness": audio.dBFS,
                "max_loudness": audio.max_dBFS,
                "rms": audio.rms
            }
            
            # Convert to numpy array for advanced analysis
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2))
                samples = samples.mean(axis=1)  # Convert to mono
            
            # Calculate additional features
            analysis.update({
                "zero_crossing_rate": self._calculate_zcr(samples),
                "spectral_centroid": self._calculate_spectral_centroid(samples, audio.frame_rate),
                "energy": np.sum(samples ** 2) / len(samples)
            })
            
            # Clean up
            os.unlink(temp_file_path)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Voice analysis error: {e}")
            return {}
    
    def _calculate_zcr(self, samples: np.ndarray) -> float:
        """Calculate zero crossing rate"""
        try:
            signs = np.sign(samples)
            zero_crossings = np.sum(np.abs(np.diff(signs))) / 2
            return zero_crossings / len(samples)
        except:
            return 0.0
    
    def _calculate_spectral_centroid(self, samples: np.ndarray, sample_rate: int) -> float:
        """Calculate spectral centroid"""
        try:
            # Simple spectral centroid calculation
            fft = np.abs(np.fft.fft(samples))
            freqs = np.fft.fftfreq(len(fft), 1/sample_rate)
            
            # Only use positive frequencies
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = fft[:len(fft)//2]
            
            if np.sum(positive_fft) == 0:
                return 0.0
            
            centroid = np.sum(positive_freqs * positive_fft) / np.sum(positive_fft)
            return centroid
        except:
            return 0.0
    
    async def create_voice_profile(self, user_id: str, audio_samples: List[bytes]) -> Dict[str, Any]:
        """Create a voice profile for a user"""
        try:
            if len(audio_samples) < 3:
                raise ValueError("At least 3 audio samples required for voice profile")
            
            analyses = []
            for audio_data in audio_samples:
                analysis = await self.analyze_voice(audio_data)
                if analysis:
                    analyses.append(analysis)
            
            if not analyses:
                raise ValueError("No valid audio samples for analysis")
            
            # Calculate average characteristics
            profile = {
                "user_id": user_id,
                "sample_count": len(analyses),
                "avg_duration": np.mean([a["duration"] for a in analyses]),
                "avg_loudness": np.mean([a["loudness"] for a in analyses]),
                "avg_zcr": np.mean([a["zero_crossing_rate"] for a in analyses]),
                "avg_spectral_centroid": np.mean([a["spectral_centroid"] for a in analyses]),
                "created_at": asyncio.get_event_loop().time()
            }
            
            self.voice_profiles[user_id] = profile
            logger.info(f"Created voice profile for user {user_id}")
            
            return profile
            
        except Exception as e:
            logger.error(f"Voice profile creation error: {e}")
            return {}
    
    async def verify_voice(self, user_id: str, audio_data: bytes) -> Dict[str, Any]:
        """Verify voice against stored profile"""
        try:
            if user_id not in self.voice_profiles:
                return {"verified": False, "reason": "No voice profile found"}
            
            profile = self.voice_profiles[user_id]
            analysis = await self.analyze_voice(audio_data)
            
            if not analysis:
                return {"verified": False, "reason": "Audio analysis failed"}
            
            # Simple similarity calculation
            features = ["avg_loudness", "avg_zcr", "avg_spectral_centroid"]
            similarities = []
            
            for feature in features:
                if feature in profile and feature.replace("avg_", "") in analysis:
                    profile_val = profile[feature]
                    analysis_val = analysis[feature.replace("avg_", "")]
                    
                    if profile_val != 0:
                        similarity = 1 - abs(profile_val - analysis_val) / abs(profile_val)
                        similarities.append(max(0, similarity))
            
            if not similarities:
                return {"verified": False, "reason": "No comparable features"}
            
            overall_similarity = np.mean(similarities)
            threshold = 0.7  # Adjust based on requirements
            
            return {
                "verified": overall_similarity >= threshold,
                "confidence": overall_similarity,
                "threshold": threshold,
                "features_compared": len(similarities)
            }
            
        except Exception as e:
            logger.error(f"Voice verification error: {e}")
            return {"verified": False, "reason": f"Error: {str(e)}"}
    
    async def cleanup(self):
        """Cleanup ultra-advanced voice service"""
        try:
            # Stop all background processes
            await self.stop_wake_word_detection()
            await self.stop_real_time_processing()

            # Cleanup pygame
            if pygame.mixer.get_init():
                pygame.mixer.quit()

            # Clear buffers and profiles
            self.audio_buffer.clear()
            self.voice_profiles.clear()
            self.conversation_context.clear()

            logger.info("Ultra-advanced voice service cleanup completed")
        except Exception as e:
            logger.error(f"Voice service cleanup error: {e}")

# Alias for backward compatibility
VoiceService = UltraAdvancedVoiceService
