import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Switch,
  Slider,
  Chip,
  <PERSON>ert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Home,
  Lightbulb,
  Thermostat,
  Security,
  Camera,
  Lock,
  Sensors,
  WaterDrop,
  LocalFlorist,
  Kitchen,
  Tv,
  Speaker,
} from '@mui/icons-material';

interface SmartDevice {
  id: string;
  name: string;
  room: string;
  type: string;
  status: boolean;
  value?: number;
  icon: React.ReactNode;
}

const SmartHome: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [devices, setDevices] = useState<SmartDevice[]>([
    // Living Room
    { id: '1', name: 'Main Lights', room: 'living-room', type: 'light', status: true, value: 80, icon: <Lightbulb /> },
    { id: '2', name: 'TV', room: 'living-room', type: 'entertainment', status: false, icon: <Tv /> },
    { id: '3', name: 'Sound System', room: 'living-room', type: 'entertainment', status: true, value: 45, icon: <Speaker /> },
    
    // Kitchen
    { id: '4', name: 'Kitchen Lights', room: 'kitchen', type: 'light', status: true, value: 90, icon: <Lightbulb /> },
    { id: '5', name: 'Coffee Maker', room: 'kitchen', type: 'appliance', status: false, icon: <Kitchen /> },
    
    // Bedroom
    { id: '6', name: 'Bedroom Lights', room: 'bedroom', type: 'light', status: false, value: 30, icon: <Lightbulb /> },
    { id: '7', name: 'Thermostat', room: 'bedroom', type: 'climate', status: true, value: 72, icon: <Thermostat /> },
    
    // Security
    { id: '8', name: 'Front Door Lock', room: 'security', type: 'security', status: true, icon: <Lock /> },
    { id: '9', name: 'Security Camera', room: 'security', type: 'security', status: true, icon: <Camera /> },
    { id: '10', name: 'Motion Sensor', room: 'security', type: 'security', status: true, icon: <Sensors /> },
    
    // Garden
    { id: '11', name: 'Garden Sprinkler', room: 'garden', type: 'irrigation', status: false, icon: <WaterDrop /> },
    { id: '12', name: 'Garden Lights', room: 'garden', type: 'light', status: false, value: 60, icon: <LocalFlorist /> },
  ]);

  const rooms = [
    { id: 'living-room', name: 'Living Room', icon: <Home /> },
    { id: 'kitchen', name: 'Kitchen', icon: <Kitchen /> },
    { id: 'bedroom', name: 'Bedroom', icon: <Home /> },
    { id: 'security', name: 'Security', icon: <Security /> },
    { id: 'garden', name: 'Garden', icon: <LocalFlorist /> },
  ];

  const toggleDevice = (deviceId: string) => {
    setDevices(prev => prev.map(device => 
      device.id === deviceId ? { ...device, status: !device.status } : device
    ));
  };

  const updateDeviceValue = (deviceId: string, value: number) => {
    setDevices(prev => prev.map(device => 
      device.id === deviceId ? { ...device, value } : device
    ));
  };

  const getDevicesByRoom = (roomId: string) => {
    return devices.filter(device => device.room === roomId);
  };

  const getRoomStatus = (roomId: string) => {
    const roomDevices = getDevicesByRoom(roomId);
    const activeDevices = roomDevices.filter(device => device.status).length;
    return `${activeDevices}/${roomDevices.length} active`;
  };

  const DeviceCard: React.FC<{ device: SmartDevice }> = ({ device }) => (
    <Card sx={{ bgcolor: '#1a1a1a', height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ color: device.status ? '#00d4ff' : '#666' }}>
              {device.icon}
            </Box>
            <Typography variant="h6" noWrap>
              {device.name}
            </Typography>
          </Box>
          <Switch
            checked={device.status}
            onChange={() => toggleDevice(device.id)}
            color="primary"
          />
        </Box>

        {device.value !== undefined && (
          <Box>
            <Typography variant="body2" gutterBottom>
              {device.type === 'light' ? 'Brightness' : 
               device.type === 'climate' ? 'Temperature' : 'Level'}: {device.value}
              {device.type === 'climate' ? '°F' : '%'}
            </Typography>
            <Slider
              value={device.value}
              min={device.type === 'climate' ? 60 : 0}
              max={device.type === 'climate' ? 85 : 100}
              onChange={(_, value) => updateDeviceValue(device.id, value as number)}
              disabled={!device.status}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const RoomOverview: React.FC = () => (
    <Grid container spacing={3}>
      {rooms.map((room) => {
        const roomDevices = getDevicesByRoom(room.id);
        const activeDevices = roomDevices.filter(device => device.status).length;
        
        return (
          <Grid item xs={12} sm={6} md={4} key={room.id}>
            <Card 
              sx={{ 
                bgcolor: '#1a1a1a', 
                cursor: 'pointer',
                '&:hover': { bgcolor: '#2a2a2a' }
              }}
              onClick={() => {
                const roomIndex = rooms.findIndex(r => r.id === room.id);
                setCurrentTab(roomIndex + 1);
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Box sx={{ color: '#00d4ff' }}>
                    {room.icon}
                  </Box>
                  <Typography variant="h6">
                    {room.name}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {roomDevices.length} devices
                </Typography>
                <Chip
                  label={`${activeDevices} active`}
                  color={activeDevices > 0 ? 'success' : 'default'}
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>
        );
      })}
    </Grid>
  );

  const currentRoom = currentTab === 0 ? null : rooms[currentTab - 1];
  const currentRoomDevices = currentRoom ? getDevicesByRoom(currentRoom.id) : [];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Smart Home Control
      </Typography>

      <Alert 
        severity="success" 
        sx={{ mb: 3, bgcolor: '#1a1a1a', border: '1px solid #4caf50' }}
      >
        All systems operational. {devices.filter(d => d.status).length} of {devices.length} devices are active.
      </Alert>

      <Tabs 
        value={currentTab} 
        onChange={(_, newValue) => setCurrentTab(newValue)}
        sx={{ mb: 3 }}
      >
        <Tab label="Overview" />
        {rooms.map((room) => (
          <Tab key={room.id} label={room.name} />
        ))}
      </Tabs>

      {currentTab === 0 ? (
        <RoomOverview />
      ) : (
        <Box>
          <Typography variant="h5" gutterBottom>
            {currentRoom?.name} - {getRoomStatus(currentRoom?.id || '')}
          </Typography>
          
          <Grid container spacing={3}>
            {currentRoomDevices.map((device) => (
              <Grid item xs={12} sm={6} md={4} key={device.id}>
                <DeviceCard device={device} />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Quick Actions */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Button 
              variant="outlined" 
              onClick={() => devices.forEach(device => {
                if (device.type === 'light') toggleDevice(device.id);
              })}
            >
              All Lights Off
            </Button>
            <Button 
              variant="outlined"
              onClick={() => devices.forEach(device => {
                if (device.room === 'security') toggleDevice(device.id);
              })}
            >
              Arm Security
            </Button>
            <Button variant="outlined">
              Good Night Mode
            </Button>
            <Button variant="outlined">
              Away Mode
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Voice Commands */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Voice Commands
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label="Turn on living room lights" size="small" />
            <Chip label="Set temperature to 72 degrees" size="small" />
            <Chip label="Lock the front door" size="small" />
            <Chip label="Turn off all lights" size="small" />
            <Chip label="Activate security mode" size="small" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SmartHome;
