import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>rid,
  Switch,
  Slider,
  IconButton,
  Chip,
  Alert,
} from '@mui/material';
import {
  Smartphone,
  Laptop,
  Tv,
  Speaker,
  Lightbulb,
  Thermostat,
  VolumeUp,
  VolumeDown,
  Brightness6,
  Wifi,
  Bluetooth,
  Battery90,
  PowerSettingsNew,
} from '@mui/icons-material';

interface Device {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline';
  battery?: number;
  volume?: number;
  brightness?: number;
  temperature?: number;
  icon: React.ReactNode;
}

const DeviceControl: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([
    {
      id: '1',
      name: 'iPhone 15',
      type: 'smartphone',
      status: 'online',
      battery: 85,
      volume: 70,
      brightness: 80,
      icon: <Smartphone />,
    },
    {
      id: '2',
      name: 'MacBook Pro',
      type: 'laptop',
      status: 'online',
      battery: 92,
      volume: 50,
      brightness: 60,
      icon: <Laptop />,
    },
    {
      id: '3',
      name: 'Living Room TV',
      type: 'tv',
      status: 'offline',
      volume: 30,
      brightness: 75,
      icon: <Tv />,
    },
    {
      id: '4',
      name: '<PERSON> Speaker',
      type: 'speaker',
      status: 'online',
      volume: 40,
      icon: <Speaker />,
    },
    {
      id: '5',
      name: 'Smart Bulb',
      type: 'light',
      status: 'online',
      brightness: 90,
      icon: <Lightbulb />,
    },
    {
      id: '6',
      name: 'Thermostat',
      type: 'thermostat',
      status: 'online',
      temperature: 72,
      icon: <Thermostat />,
    },
  ]);

  const updateDevice = (deviceId: string, updates: Partial<Device>) => {
    setDevices(prev => prev.map(device => 
      device.id === deviceId ? { ...device, ...updates } : device
    ));
  };

  const toggleDeviceStatus = (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (device) {
      updateDevice(deviceId, { 
        status: device.status === 'online' ? 'offline' : 'online' 
      });
    }
  };

  const DeviceCard: React.FC<{ device: Device }> = ({ device }) => (
    <Card sx={{ bgcolor: '#1a1a1a', height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ color: device.status === 'online' ? '#00d4ff' : '#666' }}>
              {device.icon}
            </Box>
            <Typography variant="h6" noWrap>
              {device.name}
            </Typography>
          </Box>
          <Chip
            label={device.status}
            color={device.status === 'online' ? 'success' : 'error'}
            size="small"
          />
        </Box>

        {/* Power Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="body2">Power</Typography>
          <Switch
            checked={device.status === 'online'}
            onChange={() => toggleDeviceStatus(device.id)}
            color="primary"
          />
        </Box>

        {/* Battery Level */}
        {device.battery !== undefined && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">Battery</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Battery90 fontSize="small" />
                <Typography variant="body2">{device.battery}%</Typography>
              </Box>
            </Box>
          </Box>
        )}

        {/* Volume Control */}
        {device.volume !== undefined && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">Volume</Typography>
              <Typography variant="body2">{device.volume}%</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <VolumeDown fontSize="small" />
              <Slider
                value={device.volume}
                onChange={(_, value) => updateDevice(device.id, { volume: value as number })}
                disabled={device.status === 'offline'}
                sx={{ flexGrow: 1 }}
              />
              <VolumeUp fontSize="small" />
            </Box>
          </Box>
        )}

        {/* Brightness Control */}
        {device.brightness !== undefined && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">Brightness</Typography>
              <Typography variant="body2">{device.brightness}%</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Brightness6 fontSize="small" />
              <Slider
                value={device.brightness}
                onChange={(_, value) => updateDevice(device.id, { brightness: value as number })}
                disabled={device.status === 'offline'}
                sx={{ flexGrow: 1 }}
              />
            </Box>
          </Box>
        )}

        {/* Temperature Control */}
        {device.temperature !== undefined && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2">Temperature</Typography>
              <Typography variant="body2">{device.temperature}°F</Typography>
            </Box>
            <Slider
              value={device.temperature}
              min={60}
              max={85}
              onChange={(_, value) => updateDevice(device.id, { temperature: value as number })}
              disabled={device.status === 'offline'}
              marks={[
                { value: 60, label: '60°' },
                { value: 72, label: '72°' },
                { value: 85, label: '85°' },
              ]}
            />
          </Box>
        )}

        {/* Quick Actions */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {device.type === 'smartphone' && (
            <>
              <Button size="small" startIcon={<Wifi />} disabled={device.status === 'offline'}>
                WiFi
              </Button>
              <Button size="small" startIcon={<Bluetooth />} disabled={device.status === 'offline'}>
                Bluetooth
              </Button>
            </>
          )}
          {device.type === 'tv' && (
            <Button size="small" startIcon={<PowerSettingsNew />} disabled={device.status === 'offline'}>
              Input
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  const onlineDevices = devices.filter(d => d.status === 'online').length;
  const totalDevices = devices.length;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Typography variant="h4" gutterBottom>
        Device Control
      </Typography>
      
      {/* Status Overview */}
      <Alert 
        severity="info" 
        sx={{ mb: 3, bgcolor: '#1a1a1a', border: '1px solid #00d4ff' }}
      >
        {onlineDevices} of {totalDevices} devices are online and ready for control
      </Alert>

      {/* Devices Grid */}
      <Grid container spacing={3}>
        {devices.map((device) => (
          <Grid item xs={12} sm={6} md={4} key={device.id}>
            <DeviceCard device={device} />
          </Grid>
        ))}
      </Grid>

      {/* Voice Commands Help */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Voice Commands
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Try these voice commands to control your devices:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label="Turn on the TV" size="small" />
            <Chip label="Set volume to 50%" size="small" />
            <Chip label="Increase brightness" size="small" />
            <Chip label="Set temperature to 72 degrees" size="small" />
            <Chip label="Turn off all lights" size="small" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DeviceControl;
