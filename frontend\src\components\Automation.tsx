import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Grid,
  Switch,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  Chip,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  AutoAwesome,
  Schedule,
  SmartToy,
  Workflow,
  PlayArrow,
  Stop,
  Add,
  Edit,
  Delete,
  Lightbulb,
  Thermostat,
  Security,
  Email,
  Sms,
  ShoppingCart,
  Event,
} from '@mui/icons-material';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: string;
  actions: string[];
  enabled: boolean;
  category: string;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: Array<{ action: string; parameters: any }>;
  status: 'active' | 'paused' | 'stopped';
  lastRun?: Date;
}

const Automation: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [createDialogO<PERSON>, setCreateDialogOpen] = useState(false);
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([
    {
      id: '1',
      name: 'Good Morning Routine',
      description: 'Turn on lights, adjust temperature, and read news when alarm goes off',
      trigger: 'Time: 7:00 AM',
      actions: ['Turn on bedroom lights', 'Set temperature to 72°F', 'Read morning news'],
      enabled: true,
      category: 'smart-home',
    },
    {
      id: '2',
      name: 'Security Alert',
      description: 'Send notification when motion detected at night',
      trigger: 'Motion detected + Time: 10 PM - 6 AM',
      actions: ['Send SMS alert', 'Turn on security lights', 'Record video'],
      enabled: true,
      category: 'security',
    },
    {
      id: '3',
      name: 'Shopping Assistant',
      description: 'Auto-order groceries when items are low',
      trigger: 'Inventory low',
      actions: ['Check shopping list', 'Compare prices', 'Place order'],
      enabled: false,
      category: 'shopping',
    },
  ]);

  const [workflows, setWorkflows] = useState<Workflow[]>([
    {
      id: '1',
      name: 'Daily Health Check',
      description: 'Monitor vital signs and send health report',
      steps: [
        { action: 'Read vital signs', parameters: {} },
        { action: 'Analyze trends', parameters: {} },
        { action: 'Generate report', parameters: {} },
        { action: 'Send to doctor', parameters: { email: '<EMAIL>' } },
      ],
      status: 'active',
      lastRun: new Date(),
    },
    {
      id: '2',
      name: 'Meeting Preparation',
      description: 'Prepare for upcoming meetings automatically',
      steps: [
        { action: 'Check calendar', parameters: {} },
        { action: 'Gather meeting materials', parameters: {} },
        { action: 'Set up room', parameters: {} },
        { action: 'Send reminders', parameters: {} },
      ],
      status: 'active',
    },
  ]);

  const toggleAutomation = (id: string) => {
    setAutomationRules(prev => prev.map(rule => 
      rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
    ));
  };

  const toggleWorkflow = (id: string) => {
    setWorkflows(prev => prev.map(workflow => 
      workflow.id === id ? { 
        ...workflow, 
        status: workflow.status === 'active' ? 'paused' : 'active' 
      } : workflow
    ));
  };

  const AutomationRulesTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Automation Rules
            </Typography>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setCreateDialogOpen(true)}
            >
              Create Rule
            </Button>
          </Box>

          <List>
            {automationRules.map((rule) => (
              <ListItem key={rule.id}>
                <ListItemIcon>
                  {rule.category === 'smart-home' ? <Lightbulb /> :
                   rule.category === 'security' ? <Security /> :
                   rule.category === 'shopping' ? <ShoppingCart /> : <AutoAwesome />}
                </ListItemIcon>
                <ListItemText
                  primary={rule.name}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {rule.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Trigger: {rule.trigger}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        {rule.actions.map((action, index) => (
                          <Chip key={index} label={action} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                        ))}
                      </Box>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Switch
                    checked={rule.enabled}
                    onChange={() => toggleAutomation(rule.id)}
                    color="primary"
                  />
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: '#1a1a1a' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon><Schedule /></ListItemIcon>
                  <ListItemText primary="Schedule automation" />
                  <Button size="small">Setup</Button>
                </ListItem>
                <ListItem>
                  <ListItemIcon><SmartToy /></ListItemIcon>
                  <ListItemText primary="AI-powered rules" />
                  <Button size="small">Enable</Button>
                </ListItem>
                <ListItem>
                  <ListItemIcon><Workflow /></ListItemIcon>
                  <ListItemText primary="Import workflow" />
                  <Button size="small">Import</Button>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: '#1a1a1a' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Automation Stats
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Active Rules: {automationRules.filter(r => r.enabled).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Rules: {automationRules.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Success Rate: 98.5%
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const WorkflowsTab = () => (
    <Box>
      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Workflows
            </Typography>
            <Button variant="contained" startIcon={<Add />}>
              Create Workflow
            </Button>
          </Box>

          <List>
            {workflows.map((workflow) => (
              <ListItem key={workflow.id}>
                <ListItemIcon>
                  <Workflow />
                </ListItemIcon>
                <ListItemText
                  primary={workflow.name}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {workflow.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {workflow.steps.length} steps • Last run: {workflow.lastRun?.toLocaleString() || 'Never'}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Chip
                          label={workflow.status}
                          color={workflow.status === 'active' ? 'success' : 'warning'}
                          size="small"
                        />
                      </Box>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Button
                    size="small"
                    startIcon={workflow.status === 'active' ? <Stop /> : <PlayArrow />}
                    onClick={() => toggleWorkflow(workflow.id)}
                  >
                    {workflow.status === 'active' ? 'Pause' : 'Start'}
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const SmartSuggestionsTab = () => (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        JARVIS learns from your behavior and suggests new automation opportunities.
      </Alert>

      <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            AI Suggestions
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon><Lightbulb color="warning" /></ListItemIcon>
              <ListItemText
                primary="Energy Saving Automation"
                secondary="Turn off lights automatically when no motion detected for 30 minutes"
              />
              <Button size="small" variant="outlined">
                Create Rule
              </Button>
            </ListItem>
            
            <ListItem>
              <ListItemIcon><Event color="info" /></ListItemIcon>
              <ListItemText
                primary="Meeting Room Preparation"
                secondary="Automatically prepare meeting room 15 minutes before scheduled meetings"
              />
              <Button size="small" variant="outlined">
                Create Rule
              </Button>
            </ListItem>
            
            <ListItem>
              <ListItemIcon><ShoppingCart color="success" /></ListItemIcon>
              <ListItemText
                primary="Smart Grocery Ordering"
                secondary="Order groceries based on consumption patterns and calendar events"
              />
              <Button size="small" variant="outlined">
                Create Rule
              </Button>
            </ListItem>
          </List>
        </CardContent>
      </Card>

      <Card sx={{ bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Learning Insights
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Based on your usage patterns, JARVIS has identified these optimization opportunities:
          </Typography>
          <List>
            <ListItem>
              <ListItemText
                primary="Peak Usage: 7-9 AM, 6-8 PM"
                secondary="Consider scheduling energy-intensive tasks during off-peak hours"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="Frequent Actions: Light control, Temperature adjustment"
                secondary="These could be automated based on presence and time"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="Routine Patterns: Morning routine very consistent"
                secondary="High automation potential for morning activities"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const tabs = [
    { label: 'Rules', component: <AutomationRulesTab /> },
    { label: 'Workflows', component: <WorkflowsTab /> },
    { label: 'AI Suggestions', component: <SmartSuggestionsTab /> },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Automation & Workflows
      </Typography>

      <Alert severity="success" sx={{ mb: 3 }}>
        {automationRules.filter(r => r.enabled).length} automation rules are currently active and monitoring your environment.
      </Alert>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabs.map((tab, index) => (
          <Tab key={index} label={tab.label} />
        ))}
      </Tabs>

      {tabs[currentTab].component}

      {/* Create Rule Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Automation Rule</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField fullWidth label="Rule Name" placeholder="Enter rule name" />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                placeholder="Describe what this rule does"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Trigger Type</InputLabel>
                <Select defaultValue="">
                  <MenuItem value="time">Time-based</MenuItem>
                  <MenuItem value="sensor">Sensor-based</MenuItem>
                  <MenuItem value="event">Event-based</MenuItem>
                  <MenuItem value="voice">Voice command</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select defaultValue="">
                  <MenuItem value="smart-home">Smart Home</MenuItem>
                  <MenuItem value="security">Security</MenuItem>
                  <MenuItem value="health">Health</MenuItem>
                  <MenuItem value="productivity">Productivity</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Create Rule</Button>
        </DialogActions>
      </Dialog>

      {/* Voice Commands */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Voice Commands
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label="Create automation rule" size="small" />
            <Chip label="Enable morning routine" size="small" />
            <Chip label="Pause all automations" size="small" />
            <Chip label="Show automation status" size="small" />
            <Chip label="Run workflow now" size="small" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Automation;
