# Core Framework - Essential for basic functionality
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
python-multipart==0.0.6

# Database - Core only
sqlalchemy==2.0.23
redis==5.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Basic AI
openai==1.3.7

# Utilities
python-dotenv==1.0.0
aiofiles==23.2.1

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Monitoring & Logging
loguru==0.7.2

# Templating
jinja2==3.1.2
