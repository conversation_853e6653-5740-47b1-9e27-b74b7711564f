"""
Configuration settings for JARVIS backend
"""

import os
from typing import List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

def config(key: str, default=None, cast=None):
    """Simple config function to get environment variables"""
    value = os.getenv(key, default)
    if cast and value is not None:
        try:
            if cast == bool:
                # Handle both string and boolean defaults
                if isinstance(value, bool):
                    return value
                return str(value).lower() in ('true', '1', 'yes', 'on')
            return cast(value)
        except (ValueError, TypeError, AttributeError):
            return default
    return value

class Settings(BaseSettings):
    """Application settings"""
    
    # Basic Configuration
    DEBUG: bool = config("DEBUG", default="false", cast=bool)
    LOG_LEVEL: str = config("LOG_LEVEL", default="INFO")
    SECRET_KEY: str = config("SECRET_KEY", default="your-secret-key-here")
    ALGORITHM: str = config("ALGORITHM", default="HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = config("ACCESS_TOKEN_EXPIRE_MINUTES", default="30", cast=int)
    
    # Database
    DATABASE_URL: str = config("DATABASE_URL", default="sqlite:///./jarvis.db")
    REDIS_URL: str = config("REDIS_URL", default="redis://localhost:6379/0")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = config(
        "ALLOWED_ORIGINS", 
        default="http://localhost:3000,http://localhost:5173",
        cast=lambda v: [i.strip() for i in v.split(',')]
    )
    
    # OpenAI
    OPENAI_API_KEY: str = config("OPENAI_API_KEY", default="")
    OPENAI_MODEL: str = config("OPENAI_MODEL", default="gpt-4-turbo-preview")
    
    # LiveKit
    LIVEKIT_API_KEY: str = config("LIVEKIT_API_KEY", default="")
    LIVEKIT_API_SECRET: str = config("LIVEKIT_API_SECRET", default="")
    LIVEKIT_URL: str = config("LIVEKIT_URL", default="wss://localhost:7880")
    
    # N8N
    N8N_URL: str = config("N8N_URL", default="http://localhost:5678")
    N8N_API_KEY: str = config("N8N_API_KEY", default="")
    
    # External APIs
    WEATHER_API_KEY: str = config("WEATHER_API_KEY", default="")
    GOOGLE_API_KEY: str = config("GOOGLE_API_KEY", default="")
    GOOGLE_CSE_ID: str = config("GOOGLE_CSE_ID", default="")
    TWILIO_ACCOUNT_SID: str = config("TWILIO_ACCOUNT_SID", default="")
    TWILIO_AUTH_TOKEN: str = config("TWILIO_AUTH_TOKEN", default="")
    STABILITY_API_KEY: str = config("STABILITY_API_KEY", default="")
    
    # Email
    MAIL_USERNAME: str = config("MAIL_USERNAME", default="")
    MAIL_PASSWORD: str = config("MAIL_PASSWORD", default="")
    MAIL_FROM: str = config("MAIL_FROM", default="")
    MAIL_PORT: int = config("MAIL_PORT", default="587", cast=int)
    MAIL_SERVER: str = config("MAIL_SERVER", default="smtp.gmail.com")
    MAIL_TLS: bool = config("MAIL_TLS", default="true", cast=bool)
    MAIL_SSL: bool = config("MAIL_SSL", default="false", cast=bool)
    
    # Social Media
    TWITTER_API_KEY: str = config("TWITTER_API_KEY", default="")
    TWITTER_API_SECRET: str = config("TWITTER_API_SECRET", default="")
    TWITTER_ACCESS_TOKEN: str = config("TWITTER_ACCESS_TOKEN", default="")
    TWITTER_ACCESS_TOKEN_SECRET: str = config("TWITTER_ACCESS_TOKEN_SECRET", default="")
    
    # Cloud Storage
    AWS_ACCESS_KEY_ID: str = config("AWS_ACCESS_KEY_ID", default="")
    AWS_SECRET_ACCESS_KEY: str = config("AWS_SECRET_ACCESS_KEY", default="")
    AWS_REGION: str = config("AWS_REGION", default="us-east-1")
    AWS_BUCKET_NAME: str = config("AWS_BUCKET_NAME", default="")
    
    # Home Assistant
    HOME_ASSISTANT_URL: str = config("HOME_ASSISTANT_URL", default="http://localhost:8123")
    HOME_ASSISTANT_TOKEN: str = config("HOME_ASSISTANT_TOKEN", default="")
    
    # File Upload
    MAX_FILE_SIZE: str = config("MAX_FILE_SIZE", default="50MB")
    UPLOAD_DIR: str = config("UPLOAD_DIR", default="./uploads")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = config("RATE_LIMIT_PER_MINUTE", default="60", cast=int)
    
    # Celery
    CELERY_BROKER_URL: str = config("CELERY_BROKER_URL", default="redis://localhost:6379/1")
    CELERY_RESULT_BACKEND: str = config("CELERY_RESULT_BACKEND", default="redis://localhost:6379/2")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"  # Allow extra fields from .env

# Global settings instance
settings = Settings()
