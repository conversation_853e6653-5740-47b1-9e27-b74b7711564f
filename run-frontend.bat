@echo off
echo ========================================
echo    JARVIS Frontend Startup Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

echo [1/3] Checking Node.js version...
node --version
npm --version

echo.
echo [2/3] Installing dependencies...
cd frontend

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing npm packages...
    npm install
) else (
    echo Dependencies already installed. Checking for updates...
    npm update
)

echo.
echo [3/3] Starting JARVIS Frontend...
echo.
echo Frontend will be available at: http://localhost:3000
echo Make sure the backend is running at: http://localhost:8000
echo.

REM Start the frontend development server
npm run dev

pause
