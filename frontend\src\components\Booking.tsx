import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  TimePicker,
} from '@mui/material';
import {
  Flight,
  Hotel,
  Restaurant,
  LocalHospital,
  DirectionsCar,
  Event,
  Search,
  BookOnline,
  Schedule,
  LocationOn,
  Person,
  AttachMoney,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import axios from 'axios';

interface BookingResult {
  id: string;
  type: string;
  title: string;
  description: string;
  price: number;
  availability: boolean;
  provider: string;
  rating?: number;
}

const Booking: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [searchResults, setSearchResults] = useState<BookingResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [bookings, setBookings] = useState([
    {
      id: '1',
      type: 'flight',
      title: 'New York to Los Angeles',
      date: '2024-01-15',
      time: '14:30',
      status: 'confirmed',
      confirmation: 'ABC123',
    },
    {
      id: '2',
      type: 'restaurant',
      title: 'Italian Bistro',
      date: '2024-01-10',
      time: '19:00',
      status: 'confirmed',
      confirmation: 'RES456',
    },
  ]);

  const FlightBookingTab = () => {
    const [departure, setDeparture] = useState('');
    const [destination, setDestination] = useState('');
    const [departureDate, setDepartureDate] = useState<Date | null>(null);
    const [returnDate, setReturnDate] = useState<Date | null>(null);
    const [passengers, setPassengers] = useState(1);
    const [tripType, setTripType] = useState('roundtrip');

    const searchFlights = async () => {
      setIsSearching(true);
      try {
        const response = await axios.post('/api/v1/booking/search-flights', {
          departure,
          destination,
          departureDate,
          returnDate,
          passengers,
          tripType,
        });
        setSearchResults(response.data.flights || []);
      } catch (error) {
        console.error('Flight search failed:', error);
      } finally {
        setIsSearching(false);
      }
    };

    return (
      <Box>
        <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Flight Search
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="From"
                  value={departure}
                  onChange={(e) => setDeparture(e.target.value)}
                  placeholder="Departure city"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="To"
                  value={destination}
                  onChange={(e) => setDestination(e.target.value)}
                  placeholder="Destination city"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Departure Date"
                    value={departureDate}
                    onChange={setDepartureDate}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Return Date"
                    value={returnDate}
                    onChange={setReturnDate}
                    disabled={tripType === 'oneway'}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Trip Type</InputLabel>
                  <Select value={tripType} onChange={(e) => setTripType(e.target.value)}>
                    <MenuItem value="roundtrip">Round Trip</MenuItem>
                    <MenuItem value="oneway">One Way</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Passengers"
                  type="number"
                  value={passengers}
                  onChange={(e) => setPassengers(parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 9 }}
                />
              </Grid>
            </Grid>

            <Button
              variant="contained"
              startIcon={<Search />}
              onClick={searchFlights}
              disabled={isSearching || !departure || !destination}
              sx={{ mt: 2 }}
              fullWidth
            >
              {isSearching ? 'Searching...' : 'Search Flights'}
            </Button>
          </CardContent>
        </Card>

        {searchResults.length > 0 && (
          <Card sx={{ bgcolor: '#1a1a1a' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Flight Results
              </Typography>
              <List>
                {searchResults.map((result) => (
                  <ListItem key={result.id}>
                    <ListItemIcon>
                      <Flight />
                    </ListItemIcon>
                    <ListItemText
                      primary={result.title}
                      secondary={result.description}
                    />
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="h6">${result.price}</Typography>
                      <Button size="small" variant="outlined">
                        Book Now
                      </Button>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        )}
      </Box>
    );
  };

  const HotelBookingTab = () => {
    const [location, setLocation] = useState('');
    const [checkIn, setCheckIn] = useState<Date | null>(null);
    const [checkOut, setCheckOut] = useState<Date | null>(null);
    const [guests, setGuests] = useState(2);
    const [rooms, setRooms] = useState(1);

    return (
      <Box>
        <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Hotel Search
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Destination"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="City, hotel name, or landmark"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Check-in Date"
                    value={checkIn}
                    onChange={setCheckIn}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Check-out Date"
                    value={checkOut}
                    onChange={setCheckOut}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Guests"
                  type="number"
                  value={guests}
                  onChange={(e) => setGuests(parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 10 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Rooms"
                  type="number"
                  value={rooms}
                  onChange={(e) => setRooms(parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 5 }}
                />
              </Grid>
            </Grid>

            <Button
              variant="contained"
              startIcon={<Search />}
              disabled={!location}
              sx={{ mt: 2 }}
              fullWidth
            >
              Search Hotels
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  };

  const RestaurantBookingTab = () => {
    const [restaurant, setRestaurant] = useState('');
    const [date, setDate] = useState<Date | null>(null);
    const [time, setTime] = useState<Date | null>(null);
    const [partySize, setPartySize] = useState(2);
    const [cuisine, setCuisine] = useState('');

    return (
      <Box>
        <Card sx={{ mb: 3, bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Restaurant Reservation
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Restaurant or Location"
                  value={restaurant}
                  onChange={(e) => setRestaurant(e.target.value)}
                  placeholder="Restaurant name or area"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Cuisine Type</InputLabel>
                  <Select value={cuisine} onChange={(e) => setCuisine(e.target.value)}>
                    <MenuItem value="italian">Italian</MenuItem>
                    <MenuItem value="chinese">Chinese</MenuItem>
                    <MenuItem value="mexican">Mexican</MenuItem>
                    <MenuItem value="american">American</MenuItem>
                    <MenuItem value="indian">Indian</MenuItem>
                    <MenuItem value="japanese">Japanese</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Party Size"
                  type="number"
                  value={partySize}
                  onChange={(e) => setPartySize(parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 20 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Date"
                    value={date}
                    onChange={setDate}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <TimePicker
                    label="Time"
                    value={time}
                    onChange={setTime}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
            </Grid>

            <Button
              variant="contained"
              startIcon={<Search />}
              sx={{ mt: 2 }}
              fullWidth
            >
              Find Tables
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  };

  const MyBookingsTab = () => (
    <Box>
      <Card sx={{ bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Your Bookings
          </Typography>
          
          <List>
            {bookings.map((booking) => (
              <ListItem key={booking.id}>
                <ListItemIcon>
                  {booking.type === 'flight' ? <Flight /> : 
                   booking.type === 'hotel' ? <Hotel /> : 
                   booking.type === 'restaurant' ? <Restaurant /> : <Event />}
                </ListItemIcon>
                <ListItemText
                  primary={booking.title}
                  secondary={`${booking.date} at ${booking.time} - ${booking.confirmation}`}
                />
                <Chip
                  label={booking.status}
                  color={booking.status === 'confirmed' ? 'success' : 'warning'}
                  size="small"
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const tabs = [
    { label: 'Flights', component: <FlightBookingTab /> },
    { label: 'Hotels', component: <HotelBookingTab /> },
    { label: 'Restaurants', component: <RestaurantBookingTab /> },
    { label: 'My Bookings', component: <MyBookingsTab /> },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Booking & Reservations
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Book flights, hotels, restaurants, and appointments with AI-powered recommendations.
      </Alert>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabs.map((tab, index) => (
          <Tab key={index} label={tab.label} />
        ))}
      </Tabs>

      {tabs[currentTab].component}

      {/* Voice Commands */}
      <Card sx={{ mt: 3, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Voice Commands
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip label="Book a flight to New York" size="small" />
            <Chip label="Find hotels in Paris" size="small" />
            <Chip label="Reserve dinner for tonight" size="small" />
            <Chip label="Schedule a doctor appointment" size="small" />
            <Chip label="Book a taxi for 3 PM" size="small" />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Booking;
