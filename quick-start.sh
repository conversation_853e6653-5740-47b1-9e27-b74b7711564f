#!/bin/bash

# JARVIS Ultra-Advanced AI Assistant - Quick Start Script
# This script automates the complete setup and testing process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PYTHON_VERSION="3.11"
NODE_VERSION="18"
SETUP_TYPE=${1:-"development"}  # development, production, or docker

echo -e "${BLUE}"
echo "🚀 JARVIS Ultra-Advanced AI Assistant - Quick Start"
echo "=================================================="
echo -e "${NC}"
echo -e "Setup Type: ${GREEN}$SETUP_TYPE${NC}"
echo -e "Python Required: ${GREEN}$PYTHON_VERSION+${NC}"
echo -e "Node.js Required: ${GREEN}$NODE_VERSION+${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python_version() {
    if command_exists python3; then
        PYTHON_VER=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
        if [ "$(printf '%s\n' "$PYTHON_VERSION" "$PYTHON_VER" | sort -V | head -n1)" = "$PYTHON_VERSION" ]; then
            echo -e "${GREEN}✅ Python $PYTHON_VER found${NC}"
            return 0
        else
            echo -e "${RED}❌ Python $PYTHON_VERSION+ required, found $PYTHON_VER${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Python 3 not found${NC}"
        return 1
    fi
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VER=$(node -v | sed 's/v//')
        NODE_MAJOR=$(echo $NODE_VER | cut -d. -f1)
        if [ "$NODE_MAJOR" -ge "$NODE_VERSION" ]; then
            echo -e "${GREEN}✅ Node.js $NODE_VER found${NC}"
            return 0
        else
            echo -e "${RED}❌ Node.js $NODE_VERSION+ required, found $NODE_VER${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Node.js not found${NC}"
        return 1
    fi
}

# Function to install system dependencies
install_system_deps() {
    print_section "📦 Installing System Dependencies"
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo -e "${YELLOW}Installing Linux dependencies...${NC}"
        sudo apt-get update
        sudo apt-get install -y \
            python3-dev python3-pip python3-venv \
            nodejs npm \
            postgresql postgresql-contrib \
            redis-server \
            git curl wget \
            build-essential \
            libpq-dev \
            libffi-dev \
            libssl-dev \
            portaudio19-dev \
            espeak espeak-data \
            ffmpeg \
            tesseract-ocr \
            docker.io docker-compose
            
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        echo -e "${YELLOW}Installing macOS dependencies...${NC}"
        if ! command_exists brew; then
            echo "Installing Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        
        brew update
        brew install \
            python@3.11 \
            node \
            postgresql \
            redis \
            git \
            portaudio \
            espeak \
            ffmpeg \
            tesseract \
            docker docker-compose
            
    else
        echo -e "${RED}❌ Unsupported operating system: $OSTYPE${NC}"
        echo -e "${YELLOW}Please install dependencies manually:${NC}"
        echo "- Python 3.11+"
        echo "- Node.js 18+"
        echo "- PostgreSQL 15+"
        echo "- Redis 7+"
        echo "- Git"
        echo "- Docker & Docker Compose"
        exit 1
    fi
    
    echo -e "${GREEN}✅ System dependencies installed${NC}"
}

# Function to setup environment
setup_environment() {
    print_section "🔧 Setting Up Environment"
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        echo -e "${YELLOW}Creating .env file...${NC}"
        cat > .env << 'EOF'
# JARVIS Configuration
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-ultra-secure-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# Database Configuration
DATABASE_URL=postgresql://jarvis:jarvis_password@localhost:5432/jarvis_db
REDIS_URL=redis://localhost:6379/0

# AI API Keys (Get free keys from the providers)
# Google Gemini (FREE) - https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# OpenAI (FREE $5 credit) - https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Anthropic Claude (FREE tier) - https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-haiku-20240307

# Voice Services (FREE tiers available)
# Azure Speech (FREE) - https://azure.microsoft.com/services/cognitive-services/speech-services/
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=eastus

# Computer Vision (FREE tiers available)
# Google Vision (FREE) - https://cloud.google.com/vision
GOOGLE_VISION_API_KEY=your_google_vision_key

# Weather (FREE) - https://openweathermap.org/api
OPENWEATHER_API_KEY=your_openweather_key

# News (FREE) - https://newsapi.org/
NEWS_API_KEY=your_news_api_key

# Financial Data (FREE) - https://www.alphavantage.co/
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Optional Services (for advanced features)
STRIPE_API_KEY=your_stripe_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token

# Monitoring
SENTRY_DSN=your_sentry_dsn
ENABLE_MONITORING=true
EOF
        
        echo -e "${GREEN}✅ .env file created${NC}"
        echo -e "${YELLOW}⚠️  Please edit .env file with your API keys${NC}"
        echo -e "${CYAN}📖 See API_RECOMMENDATIONS.md for free API key instructions${NC}"
    else
        echo -e "${GREEN}✅ .env file already exists${NC}"
    fi
}

# Function to setup backend
setup_backend() {
    print_section "🐍 Setting Up Backend"
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}Creating Python virtual environment...${NC}"
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    echo -e "${YELLOW}Activating virtual environment...${NC}"
    source venv/bin/activate
    
    # Upgrade pip
    echo -e "${YELLOW}Upgrading pip...${NC}"
    pip install --upgrade pip
    
    # Install requirements
    echo -e "${YELLOW}Installing Python dependencies...${NC}"
    pip install -r requirements.txt
    
    echo -e "${GREEN}✅ Backend setup complete${NC}"
    cd ..
}

# Function to setup frontend
setup_frontend() {
    print_section "⚛️ Setting Up Frontend"
    
    cd frontend
    
    # Install npm dependencies
    echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
    npm install
    
    # Create environment file
    if [ ! -f .env ]; then
        cat > .env << 'EOF'
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
REACT_APP_ENVIRONMENT=development
EOF
        echo -e "${GREEN}✅ Frontend .env file created${NC}"
    fi
    
    echo -e "${GREEN}✅ Frontend setup complete${NC}"
    cd ..
}

# Function to setup database
setup_database() {
    print_section "🗄️ Setting Up Database"
    
    if [ "$SETUP_TYPE" = "docker" ]; then
        echo -e "${YELLOW}Starting database containers...${NC}"
        docker run -d --name jarvis-postgres \
            -e POSTGRES_PASSWORD=jarvis_password \
            -e POSTGRES_DB=jarvis_db \
            -e POSTGRES_USER=jarvis \
            -p 5432:5432 \
            postgres:15-alpine
            
        docker run -d --name jarvis-redis \
            -p 6379:6379 \
            redis:7-alpine
            
        echo -e "${YELLOW}Waiting for databases to start...${NC}"
        sleep 10
    else
        echo -e "${YELLOW}Starting local database services...${NC}"
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo systemctl start postgresql
            sudo systemctl start redis-server
            
            # Create database and user
            sudo -u postgres createdb jarvis_db 2>/dev/null || true
            sudo -u postgres psql -c "CREATE USER jarvis WITH PASSWORD 'jarvis_password';" 2>/dev/null || true
            sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE jarvis_db TO jarvis;" 2>/dev/null || true
            
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew services start postgresql
            brew services start redis
            
            # Create database and user
            createdb jarvis_db 2>/dev/null || true
            psql postgres -c "CREATE USER jarvis WITH PASSWORD 'jarvis_password';" 2>/dev/null || true
            psql postgres -c "GRANT ALL PRIVILEGES ON DATABASE jarvis_db TO jarvis;" 2>/dev/null || true
        fi
    fi
    
    # Run database migrations
    echo -e "${YELLOW}Running database migrations...${NC}"
    cd backend
    source venv/bin/activate
    alembic upgrade head
    cd ..
    
    echo -e "${GREEN}✅ Database setup complete${NC}"
}

# Function to run tests
run_tests() {
    print_section "🧪 Running Tests"
    
    cd backend
    source venv/bin/activate
    
    echo -e "${YELLOW}Running backend tests...${NC}"
    pytest tests/ -v --tb=short
    
    cd ../frontend
    echo -e "${YELLOW}Running frontend tests...${NC}"
    npm test -- --watchAll=false
    
    cd ..
    echo -e "${GREEN}✅ All tests passed${NC}"
}

# Function to start services
start_services() {
    print_section "🚀 Starting Services"
    
    if [ "$SETUP_TYPE" = "docker" ]; then
        echo -e "${YELLOW}Starting with Docker Compose...${NC}"
        cd deployment
        docker-compose up -d
        cd ..
        
        echo -e "${YELLOW}Waiting for services to start...${NC}"
        sleep 30
        
    else
        echo -e "${YELLOW}Starting services in development mode...${NC}"
        
        # Start backend
        echo -e "${CYAN}Starting backend server...${NC}"
        cd backend
        source venv/bin/activate
        nohup uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
        cd ..
        
        # Start Celery worker
        echo -e "${CYAN}Starting Celery worker...${NC}"
        cd backend
        source venv/bin/activate
        nohup celery -A app.core.celery worker --loglevel=info > ../logs/celery.log 2>&1 &
        CELERY_PID=$!
        cd ..
        
        # Start frontend
        echo -e "${CYAN}Starting frontend server...${NC}"
        cd frontend
        nohup npm start > ../logs/frontend.log 2>&1 &
        FRONTEND_PID=$!
        cd ..
        
        # Save PIDs for cleanup
        echo "$BACKEND_PID" > .backend.pid
        echo "$CELERY_PID" > .celery.pid
        echo "$FRONTEND_PID" > .frontend.pid
        
        echo -e "${YELLOW}Waiting for services to start...${NC}"
        sleep 15
    fi
    
    echo -e "${GREEN}✅ Services started${NC}"
}

# Function to test services
test_services() {
    print_section "🔍 Testing Services"
    
    # Test backend health
    echo -e "${YELLOW}Testing backend health...${NC}"
    for i in {1..30}; do
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Backend is healthy${NC}"
            break
        fi
        echo -e "${CYAN}Waiting for backend... ($i/30)${NC}"
        sleep 2
    done
    
    # Test frontend
    echo -e "${YELLOW}Testing frontend...${NC}"
    for i in {1..30}; do
        if curl -f http://localhost:3000 >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Frontend is accessible${NC}"
            break
        fi
        echo -e "${CYAN}Waiting for frontend... ($i/30)${NC}"
        sleep 2
    done
    
    # Test AI service
    echo -e "${YELLOW}Testing AI service...${NC}"
    AI_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/ai/chat" \
        -H "Content-Type: application/json" \
        -d '{"message": "Hello JARVIS", "user_id": "test_user"}' | jq -r '.response' 2>/dev/null || echo "")
    
    if [ -n "$AI_RESPONSE" ] && [ "$AI_RESPONSE" != "null" ]; then
        echo -e "${GREEN}✅ AI service is working${NC}"
    else
        echo -e "${YELLOW}⚠️  AI service may need API keys configured${NC}"
    fi
    
    echo -e "${GREEN}✅ Service testing complete${NC}"
}

# Function to display access information
display_access_info() {
    print_section "🌐 Access Information"
    
    echo -e "${GREEN}🎉 JARVIS is now running!${NC}"
    echo ""
    echo -e "${CYAN}Frontend (Web Interface):${NC}"
    echo -e "  🌐 http://localhost:3000"
    echo ""
    echo -e "${CYAN}Backend API:${NC}"
    echo -e "  🔗 http://localhost:8000"
    echo -e "  📚 API Documentation: http://localhost:8000/docs"
    echo -e "  🔍 Interactive API: http://localhost:8000/redoc"
    echo ""
    
    if [ "$SETUP_TYPE" = "docker" ]; then
        echo -e "${CYAN}Monitoring & Management:${NC}"
        echo -e "  📊 Grafana: http://localhost:3001 (admin/admin)"
        echo -e "  📈 Prometheus: http://localhost:9090"
        echo -e "  🌸 Flower: http://localhost:5555"
        echo -e "  📋 Kibana: http://localhost:5601"
        echo -e "  💾 MinIO: http://localhost:9001 (jarvis/jarvis_minio_password)"
        echo ""
    fi
    
    echo -e "${YELLOW}📖 Next Steps:${NC}"
    echo -e "  1. Edit .env file with your API keys"
    echo -e "  2. See API_RECOMMENDATIONS.md for free API keys"
    echo -e "  3. See FEATURES_AND_TESTING.md for testing guide"
    echo -e "  4. See DEPLOYMENT.md for production deployment"
    echo ""
    
    echo -e "${PURPLE}🔧 Useful Commands:${NC}"
    if [ "$SETUP_TYPE" != "docker" ]; then
        echo -e "  Stop services: ./quick-start.sh stop"
        echo -e "  View logs: tail -f logs/*.log"
    else
        echo -e "  Stop services: cd deployment && docker-compose down"
        echo -e "  View logs: cd deployment && docker-compose logs -f"
    fi
    echo -e "  Run tests: ./quick-start.sh test"
    echo ""
}

# Function to stop services
stop_services() {
    print_section "🛑 Stopping Services"
    
    if [ "$SETUP_TYPE" = "docker" ]; then
        cd deployment
        docker-compose down
        cd ..
    else
        # Kill processes using PID files
        if [ -f .backend.pid ]; then
            kill $(cat .backend.pid) 2>/dev/null || true
            rm .backend.pid
        fi
        
        if [ -f .celery.pid ]; then
            kill $(cat .celery.pid) 2>/dev/null || true
            rm .celery.pid
        fi
        
        if [ -f .frontend.pid ]; then
            kill $(cat .frontend.pid) 2>/dev/null || true
            rm .frontend.pid
        fi
        
        # Kill any remaining processes
        pkill -f "uvicorn app.main:app" 2>/dev/null || true
        pkill -f "celery.*worker" 2>/dev/null || true
        pkill -f "npm start" 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Function to clean up
cleanup() {
    print_section "🧹 Cleaning Up"
    
    # Remove log files
    rm -rf logs/*.log
    
    # Remove PID files
    rm -f .*.pid
    
    # Remove Python cache
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # Remove node_modules if requested
    if [ "$1" = "full" ]; then
        rm -rf frontend/node_modules
        rm -rf backend/venv
    fi
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Create logs directory
mkdir -p logs

# Main execution flow
case "$1" in
    "stop")
        stop_services
        ;;
    "clean")
        cleanup
        ;;
    "clean-full")
        cleanup full
        ;;
    "test")
        run_tests
        ;;
    *)
        # Prerequisites check
        print_section "🔍 Checking Prerequisites"
        
        if ! check_python_version; then
            echo -e "${RED}Please install Python $PYTHON_VERSION or higher${NC}"
            exit 1
        fi
        
        if ! check_node_version; then
            echo -e "${RED}Please install Node.js $NODE_VERSION or higher${NC}"
            exit 1
        fi
        
        # Check for required commands
        MISSING_COMMANDS=()
        for cmd in git curl; do
            if ! command_exists $cmd; then
                MISSING_COMMANDS+=($cmd)
            fi
        done
        
        if [ ${#MISSING_COMMANDS[@]} -ne 0 ]; then
            echo -e "${RED}❌ Missing required commands: ${MISSING_COMMANDS[*]}${NC}"
            echo -e "${YELLOW}Installing system dependencies...${NC}"
            install_system_deps
        else
            echo -e "${GREEN}✅ All prerequisites met${NC}"
        fi
        
        # Setup process
        setup_environment
        setup_backend
        setup_frontend
        setup_database
        start_services
        test_services
        display_access_info
        ;;
esac
