#!/bin/bash

# JARVIS Optimized Deployment Script
# Deploys JARVIS with optimized tech stack (Whisper, multi-AI models, Supabase)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_TYPE=${1:-"local"}  # local, docker, cloud
ENVIRONMENT=${2:-"development"}  # development, production

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to print section headers
print_section() {
    echo
    print_color $CYAN "=================================="
    print_color $CYAN "$1"
    print_color $CYAN "=================================="
    echo
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_section "🔍 Checking Prerequisites"
    
    local requirements_met=true
    
    # Check Docker
    if command_exists docker; then
        print_color $GREEN "✅ Docker is installed"
        if docker info >/dev/null 2>&1; then
            print_color $GREEN "✅ Docker daemon is running"
        else
            print_color $RED "❌ Docker daemon is not running"
            print_color $YELLOW "Please start Docker daemon"
            requirements_met=false
        fi
    else
        print_color $RED "❌ Docker is not installed"
        print_color $YELLOW "Install: https://docs.docker.com/get-docker/"
        requirements_met=false
    fi
    
    # Check Docker Compose
    if command_exists docker-compose; then
        print_color $GREEN "✅ Docker Compose is installed"
    else
        print_color $RED "❌ Docker Compose is not installed"
        print_color $YELLOW "Install: https://docs.docker.com/compose/install/"
        requirements_met=false
    fi
    
    if [ "$requirements_met" = false ]; then
        print_color $RED "❌ Prerequisites not met. Please install missing components."
        exit 1
    fi
    
    print_color $GREEN "✅ All prerequisites met!"
}

# Function to setup environment
setup_environment() {
    print_section "🌍 Setting Up Environment"
    
    # Check if .env exists
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_color $YELLOW "⚠️  .env not found, copying from .env.example"
            cp .env.example .env
        else
            print_color $RED "❌ No .env or .env.example found"
            print_color $YELLOW "Creating basic .env file..."
            create_basic_env
        fi
    else
        print_color $GREEN "✅ .env file found"
    fi
    
    # Validate environment variables
    validate_env_vars
}

# Function to create basic .env file
create_basic_env() {
    cat > .env << 'EOF'
# JARVIS Configuration
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Configuration
DATABASE_URL=postgresql://jarvis:jarvis_password@localhost:5432/jarvis_db
REDIS_URL=redis://localhost:6379/0

# AI API Keys (Get free keys from the providers)
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Voice Services (Whisper is FREE and local)
WHISPER_MODEL=base
USE_LOCAL_WHISPER=true
USE_GTTS=true
TTS_LANGUAGE=en

# Computer Vision
GOOGLE_VISION_API_KEY=your_google_vision_key

# Optional Services
OPENWEATHER_API_KEY=your_openweather_key
NEWS_API_KEY=your_news_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# LiveKit Configuration
LIVEKIT_URL=your_livekit_url
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
ENABLE_MONITORING=true

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
EOF
    
    print_color $GREEN "✅ Basic .env file created"
    print_color $YELLOW "⚠️  Please update API keys in .env file"
}

# Function to validate environment variables
validate_env_vars() {
    print_color $BLUE "🔍 Validating environment variables..."
    
    # Check for placeholder values
    local placeholder_vars=()
    
    while IFS= read -r line; do
        if [[ $line == *"=your_"* ]] || [[ $line == *"=sk-"* ]] && [[ $line == *"_here"* ]]; then
            var_name=$(echo "$line" | cut -d'=' -f1)
            placeholder_vars+=("$var_name")
        fi
    done < .env
    
    if [ ${#placeholder_vars[@]} -gt 0 ]; then
        print_color $YELLOW "⚠️  Found placeholder values for:"
        for var in "${placeholder_vars[@]}"; do
            print_color $YELLOW "   - $var"
        done
        print_color $YELLOW "Please update these with actual values for full functionality"
    else
        print_color $GREEN "✅ Environment variables look good"
    fi
}

# Function to deploy with Docker
deploy_docker() {
    print_section "🐳 Deploying with Docker"
    
    # Build and start services
    print_color $BLUE "Building and starting services..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml up -d --build
    else
        docker-compose -f docker-compose.dev.yml up -d --build
    fi
    
    # Wait for services to be ready
    print_color $BLUE "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    check_service_health
}

# Function to check service health
check_service_health() {
    print_color $BLUE "🏥 Checking service health..."
    
    local max_retries=30
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            print_color $GREEN "✅ Backend is healthy"
            break
        fi
        
        retry_count=$((retry_count + 1))
        print_color $YELLOW "⏳ Waiting for backend... ($retry_count/$max_retries)"
        sleep 2
    done
    
    if [ $retry_count -eq $max_retries ]; then
        print_color $RED "❌ Backend failed to start"
        print_color $YELLOW "Check logs: docker-compose logs jarvis-backend"
        exit 1
    fi
    
    # Check frontend
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        print_color $GREEN "✅ Frontend is accessible"
    else
        print_color $YELLOW "⚠️  Frontend may not be ready yet"
    fi
}

# Function to run tests
run_tests() {
    print_section "🧪 Running Tests"
    
    if [ -f "test-comprehensive.py" ]; then
        print_color $BLUE "Running comprehensive test suite..."
        python3 test-comprehensive.py
    elif [ -f "test-basic.py" ]; then
        print_color $BLUE "Running basic test suite..."
        python3 test-basic.py
    else
        print_color $YELLOW "⚠️  No test files found, skipping tests"
    fi
}

# Function to show deployment summary
show_summary() {
    print_section "🎉 Deployment Complete!"
    
    print_color $GREEN "JARVIS is now running with optimized tech stack:"
    echo
    print_color $CYAN "🌐 Access URLs:"
    print_color $WHITE "   Frontend:     http://localhost:3000"
    print_color $WHITE "   Backend API:  http://localhost:8000"
    print_color $WHITE "   API Docs:     http://localhost:8000/docs"
    echo
    print_color $CYAN "🔧 Tech Stack Highlights:"
    print_color $WHITE "   ✅ Whisper (FREE speech recognition)"
    print_color $WHITE "   ✅ Multi-AI models (OpenAI, Gemini, Claude)"
    print_color $WHITE "   ✅ Supabase (cloud database)"
    print_color $WHITE "   ✅ Docker containerization"
    echo
    print_color $CYAN "📋 Next Steps:"
    print_color $WHITE "   1. Update API keys in .env file"
    print_color $WHITE "   2. Test functionality at http://localhost:3000"
    print_color $WHITE "   3. Run: python3 test-comprehensive.py"
    print_color $WHITE "   4. Deploy to cloud when ready"
    echo
    print_color $YELLOW "💡 For cloud deployment:"
    print_color $WHITE "   - Frontend: Vercel, Netlify, GitHub Pages"
    print_color $WHITE "   - Backend: Railway, Render, Fly.io"
    print_color $WHITE "   - Database: Already using Supabase!"
}

# Main deployment function
main() {
    print_color $BLUE "🚀 JARVIS Optimized Deployment"
    print_color $BLUE "Deployment Type: $DEPLOYMENT_TYPE"
    print_color $BLUE "Environment: $ENVIRONMENT"
    echo
    
    # Run deployment steps
    check_prerequisites
    setup_environment
    
    case $DEPLOYMENT_TYPE in
        "docker"|"local")
            deploy_docker
            ;;
        "cloud")
            print_color $YELLOW "Cloud deployment guide will be shown after local setup"
            deploy_docker
            ;;
        *)
            print_color $RED "❌ Unknown deployment type: $DEPLOYMENT_TYPE"
            print_color $YELLOW "Usage: $0 [local|docker|cloud] [development|production]"
            exit 1
            ;;
    esac
    
    # Run tests if requested
    if [ "$3" = "test" ]; then
        run_tests
    fi
    
    show_summary
}

# Run main function
main "$@"
