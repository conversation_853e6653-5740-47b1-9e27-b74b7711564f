import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  IconButton,
  Avatar,
  Chip,
  LinearProgress,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import {
  Mic,
  MicOff,
  Send,
  VolumeUp,
  Settings,
  History,
  Clear,
  KeyboardVoice,
} from '@mui/icons-material';
import { useJarvisStore } from '../store/jarvisStore';

const VoiceInterface: React.FC = () => {
  const [message, setMessage] = useState('');
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [historyOpen, setHistoryOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    isListening,
    transcript,
    response,
    conversationHistory,
    startListening,
    stopListening,
    sendMessage,
    clearConversation,
    isConnected,
  } = useJarvisStore();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversationHistory]);

  const handleVoiceToggle = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  const handleSendMessage = async () => {
    if (message.trim()) {
      await sendMessage(message);
      setMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      speechSynthesis.speak(utterance);
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#0a0a0a' }}>
      {/* Header */}
      <Card sx={{ m: 2, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h5" gutterBottom>
                JARVIS Voice Assistant
              </Typography>
              <Chip
                label={isConnected ? 'Connected' : 'Disconnected'}
                color={isConnected ? 'success' : 'error'}
                size="small"
              />
            </Box>
            <Box>
              <IconButton onClick={() => setHistoryOpen(true)} color="primary">
                <History />
              </IconButton>
              <IconButton onClick={() => setSettingsOpen(true)} color="primary">
                <Settings />
              </IconButton>
              <IconButton onClick={clearConversation} color="secondary">
                <Clear />
              </IconButton>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Voice Status */}
      {isListening && (
        <Card sx={{ mx: 2, mb: 2, bgcolor: '#1a1a1a' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <KeyboardVoice color="primary" />
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="body2" gutterBottom>
                  Listening...
                </Typography>
                <LinearProgress />
              </Box>
            </Box>
            {transcript && (
              <Typography variant="body2" sx={{ mt: 1, color: '#00d4ff' }}>
                "{transcript}"
              </Typography>
            )}
          </CardContent>
        </Card>
      )}

      {/* Chat Messages */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', px: 2 }}>
        {conversationHistory.length === 0 ? (
          <Card sx={{ bgcolor: '#1a1a1a', textAlign: 'center', py: 4 }}>
            <CardContent>
              <KeyboardVoice sx={{ fontSize: 64, color: '#00d4ff', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Start a conversation with JARVIS
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click the microphone button or type a message to begin
              </Typography>
            </CardContent>
          </Card>
        ) : (
          conversationHistory.map((item, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                justifyContent: item.type === 'user' ? 'flex-end' : 'flex-start',
                mb: 2,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 1,
                  maxWidth: '70%',
                  flexDirection: item.type === 'user' ? 'row-reverse' : 'row',
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: item.type === 'user' ? '#00d4ff' : '#ff6b35',
                    width: 32,
                    height: 32,
                  }}
                >
                  {item.type === 'user' ? 'U' : 'J'}
                </Avatar>
                <Card
                  sx={{
                    bgcolor: item.type === 'user' ? '#00d4ff' : '#1a1a1a',
                    color: item.type === 'user' ? '#000' : '#fff',
                  }}
                >
                  <CardContent sx={{ py: 1, px: 2, '&:last-child': { pb: 1 } }}>
                    <Typography variant="body2">
                      {item.message}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                      <Typography variant="caption" sx={{ opacity: 0.7 }}>
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </Typography>
                      {item.type === 'assistant' && (
                        <IconButton
                          size="small"
                          onClick={() => speakText(item.message)}
                          sx={{ ml: 1, color: 'inherit' }}
                        >
                          <VolumeUp fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            </Box>
          ))
        )}
        <div ref={messagesEndRef} />
      </Box>

      {/* Input Area */}
      <Card sx={{ m: 2, bgcolor: '#1a1a1a' }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              fullWidth
              multiline
              maxRows={3}
              placeholder="Type your message or use voice..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  bgcolor: '#2a2a2a',
                },
              }}
            />
            <IconButton
              color="primary"
              onClick={handleSendMessage}
              disabled={!message.trim()}
            >
              <Send />
            </IconButton>
          </Box>
        </CardContent>
      </Card>

      {/* Voice Button */}
      <Fab
        color={isListening ? "secondary" : "primary"}
        sx={{
          position: 'fixed',
          bottom: 100,
          right: 24,
          width: 64,
          height: 64,
        }}
        onClick={handleVoiceToggle}
      >
        {isListening ? <MicOff /> : <Mic />}
      </Fab>

      {/* History Dialog */}
      <Dialog
        open={historyOpen}
        onClose={() => setHistoryOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Conversation History</DialogTitle>
        <DialogContent>
          <List>
            {conversationHistory.map((item, index) => (
              <ListItem key={index}>
                <ListItemText
                  primary={`${item.type === 'user' ? 'You' : 'JARVIS'}: ${item.message}`}
                  secondary={new Date(item.timestamp).toLocaleString()}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog
        open={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Voice Settings</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            Voice settings and preferences will be available in future updates.
          </Typography>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default VoiceInterface;
